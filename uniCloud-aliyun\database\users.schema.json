{"bsonType": "object", "description": "用户信息表", "required": ["username", "password"], "properties": {"_id": {"description": "用户唯一标识"}, "username": {"bsonType": "string", "description": "用户名（学号或手机号）", "title": "用户名", "trim": "both", "minLength": 3, "maxLength": 20}, "password": {"bsonType": "string", "description": "加密后的密码", "title": "密码", "minLength": 6}, "email": {"bsonType": "string", "description": "邮箱地址", "title": "邮箱", "format": "email"}, "phone": {"bsonType": "string", "description": "手机号码", "title": "手机号", "pattern": "^1[3-9]\\d{9}$"}, "avatar": {"bsonType": "string", "description": "头像URL", "title": "头像"}, "nickname": {"bsonType": "string", "description": "昵称", "title": "昵称", "maxLength": 20}, "status": {"bsonType": "int", "description": "用户状态：0-正常，1-禁用", "title": "状态", "enum": [0, 1], "default": 0}, "createdAt": {"bsonType": "timestamp", "description": "创建时间", "title": "创建时间", "forceDefaultValue": {"$env": "now"}}, "updatedAt": {"bsonType": "timestamp", "description": "更新时间", "title": "更新时间", "forceDefaultValue": {"$env": "now"}}}, "indexes": [{"IndexName": "username", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "username", "Direction": "1"}], "MgoIsUnique": true}}]}