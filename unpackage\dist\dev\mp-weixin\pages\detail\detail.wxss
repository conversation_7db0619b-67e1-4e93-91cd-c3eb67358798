
.container.data-v-3e159eb4 {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 图片轮播 */
.image-swiper.data-v-3e159eb4 {
  width: 100%;
  height: 500rpx;
}
.house-image.data-v-3e159eb4 {
  width: 100%;
  height: 100%;
}

/* 房源基本信息 */
.house-info.data-v-3e159eb4 {
  background: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.house-title.data-v-3e159eb4 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  line-height: 1.4;
}
.price-info.data-v-3e159eb4 {
  display: flex;
  align-items: baseline;
  margin-bottom: 20rpx;
}
.price.data-v-3e159eb4 {
  font-size: 48rpx;
  font-weight: bold;
  color: #ff6b6b;
}
.price-unit.data-v-3e159eb4 {
  font-size: 28rpx;
  color: #999999;
  margin-left: 8rpx;
}
.house-details.data-v-3e159eb4 {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}
.detail-item.data-v-3e159eb4 {
  display: flex;
  align-items: center;
  margin-right: 40rpx;
  margin-bottom: 10rpx;
}
.detail-label.data-v-3e159eb4 {
  font-size: 26rpx;
  color: #666666;
}
.detail-value.data-v-3e159eb4 {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
}
.house-tags.data-v-3e159eb4 {
  display: flex;
  flex-wrap: wrap;
}
.tag.data-v-3e159eb4 {
  background: #f0f2ff;
  color: #667eea;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
  margin-bottom: 10rpx;
}

/* 通用区块 */
.section.data-v-3e159eb4 {
  background: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.section-title.data-v-3e159eb4 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}
.description.data-v-3e159eb4 {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}

/* 配套设施 */
.facilities.data-v-3e159eb4 {
  display: flex;
  flex-wrap: wrap;
}
.facility-item.data-v-3e159eb4 {
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 12rpx 20rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}
.facility-text.data-v-3e159eb4 {
  font-size: 26rpx;
  color: #333333;
}

/* 位置和联系信息 */
.address.data-v-3e159eb4 {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.5;
}
.contact-info.data-v-3e159eb4 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.contact-name.data-v-3e159eb4 {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}
.contact-phone.data-v-3e159eb4 {
  font-size: 28rpx;
  color: #667eea;
  font-weight: 500;
}

/* 浏览统计 */
.stats.data-v-3e159eb4 {
  background: #ffffff;
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.stats-text.data-v-3e159eb4 {
  font-size: 24rpx;
  color: #999999;
}

/* 底部操作栏 */
.bottom-actions.data-v-3e159eb4 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  display: flex;
  padding: 20rpx 30rpx;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.action-btn.data-v-3e159eb4 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  margin: 0 10rpx;
  border-radius: 12rpx;
}
.favorite-btn.data-v-3e159eb4 {
  background: #f8f9fa;
}
.contact-btn.data-v-3e159eb4 {
  background: #667eea;
}
.appointment-btn.data-v-3e159eb4 {
  background: #ff6b6b;
}
.action-icon.data-v-3e159eb4 {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}
.action-text.data-v-3e159eb4 {
  font-size: 24rpx;
  color: #333333;
}
.contact-btn .action-text.data-v-3e159eb4,
.appointment-btn .action-text.data-v-3e159eb4 {
  color: #ffffff;
}

/* 加载和错误状态 */
.loading.data-v-3e159eb4, .error.data-v-3e159eb4 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #f5f5f5;
}
.loading-text.data-v-3e159eb4, .error-text.data-v-3e159eb4 {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 40rpx;
}
.back-btn.data-v-3e159eb4 {
  background: #667eea;
  color: #ffffff;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
}

