{"bsonType": "object", "description": "预约看房表", "required": ["userId", "houseId", "appointmentTime"], "properties": {"_id": {"description": "预约记录唯一标识"}, "userId": {"bsonType": "string", "description": "用户ID", "title": "用户ID", "foreignKey": "users._id"}, "houseId": {"bsonType": "string", "description": "房源ID", "title": "房源ID", "foreignKey": "houses._id"}, "appointmentTime": {"bsonType": "timestamp", "description": "预约看房时间", "title": "预约时间"}, "status": {"bsonType": "string", "description": "预约状态", "title": "预约状态", "enum": ["pending", "confirmed", "cancelled", "completed"], "default": "pending"}, "note": {"bsonType": "string", "description": "用户备注", "title": "备注信息", "maxLength": 500}, "contactPhone": {"bsonType": "string", "description": "联系电话", "title": "联系电话", "pattern": "^1[3-9]\\d{9}$"}, "createdAt": {"bsonType": "timestamp", "description": "预约提交时间", "title": "提交时间", "forceDefaultValue": {"$env": "now"}}, "updatedAt": {"bsonType": "timestamp", "description": "更新时间", "title": "更新时间", "forceDefaultValue": {"$env": "now"}}}, "indexes": [{"IndexName": "userId_createdAt", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "userId", "Direction": "1"}, {"Name": "createdAt", "Direction": "-1"}]}}, {"IndexName": "houseId_appointmentTime", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "houseId", "Direction": "1"}, {"Name": "appointmentTime", "Direction": "1"}]}}, {"IndexName": "status_appointmentTime", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "status", "Direction": "1"}, {"Name": "appointmentTime", "Direction": "1"}]}}]}