/**
 * 数据库操作工具类
 * 提供常用的数据库操作方法
 */

const db = uniCloud.database()

// 获取集合引用
function getCollection(collectionName) {
  return db.collection(collectionName)
}

// 分页查询
async function paginate(collection, options = {}) {
  const {
    page = 1,
    pageSize = 10,
    where = {},
    orderBy = {},
    field = {}
  } = options
  
  const skip = (page - 1) * pageSize
  
  // 构建查询
  let query = collection.where(where)
  
  // 添加排序
  if (Object.keys(orderBy).length > 0) {
    for (const [key, value] of Object.entries(orderBy)) {
      query = query.orderBy(key, value)
    }
  }
  
  // 添加字段筛选
  if (Object.keys(field).length > 0) {
    query = query.field(field)
  }
  
  // 执行查询
  const [listResult, countResult] = await Promise.all([
    query.skip(skip).limit(pageSize).get(),
    collection.where(where).count()
  ])
  
  return {
    data: listResult.data,
    total: countResult.total,
    page,
    pageSize,
    totalPages: Math.ceil(countResult.total / pageSize)
  }
}

// 根据ID查询单条记录
async function findById(collectionName, id, field = {}) {
  const collection = getCollection(collectionName)
  let query = collection.doc(id)
  
  if (Object.keys(field).length > 0) {
    query = query.field(field)
  }
  
  const result = await query.get()
  return result.data.length > 0 ? result.data[0] : null
}

// 根据条件查询单条记录
async function findOne(collectionName, where = {}, field = {}) {
  const collection = getCollection(collectionName)
  let query = collection.where(where)
  
  if (Object.keys(field).length > 0) {
    query = query.field(field)
  }
  
  const result = await query.limit(1).get()
  return result.data.length > 0 ? result.data[0] : null
}

// 创建记录
async function create(collectionName, data) {
  const collection = getCollection(collectionName)
  const now = Date.now()
  
  const createData = {
    ...data,
    createdAt: now,
    updatedAt: now
  }
  
  const result = await collection.add(createData)
  return result.id
}

// 更新记录
async function updateById(collectionName, id, data) {
  const collection = getCollection(collectionName)
  const updateData = {
    ...data,
    updatedAt: Date.now()
  }
  
  const result = await collection.doc(id).update(updateData)
  return result.updated
}

// 删除记录
async function deleteById(collectionName, id) {
  const collection = getCollection(collectionName)
  const result = await collection.doc(id).remove()
  return result.deleted
}

// 检查记录是否存在
async function exists(collectionName, where) {
  const collection = getCollection(collectionName)
  const result = await collection.where(where).count()
  return result.total > 0
}

module.exports = {
  db,
  getCollection,
  paginate,
  findById,
  findOne,
  create,
  updateById,
  deleteById,
  exists
}
