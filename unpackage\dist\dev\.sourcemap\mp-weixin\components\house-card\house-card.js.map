{"version": 3, "sources": ["uni-app:///components/house-card/house-card.vue", "webpack:///D:/web/project/租房管理/components/house-card/house-card.vue?420f", "webpack:///D:/web/project/租房管理/components/house-card/house-card.vue?d918", "webpack:///D:/web/project/租房管理/components/house-card/house-card.vue?5473", "webpack:///D:/web/project/租房管理/components/house-card/house-card.vue?aeef", "webpack:///D:/web/project/租房管理/components/house-card/house-card.vue?8a26", "webpack:///D:/web/project/租房管理/components/house-card/house-card.vue?7cce"], "names": ["name", "props", "house", "type", "required", "methods", "goToDetail", "uni", "url"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAmDA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACAC;MACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnEA;AAAA;AAAA;AAAA;AAA47B,CAAgB,s5BAAG,EAAC,C;;;;;;;;;;;ACAh9B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACqC;;;AAG9F;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAAqoB,CAAgB,moBAAG,EAAC,C", "file": "components/house-card/house-card.js", "sourcesContent": ["<template>\n  <view class=\"house-card\" @click=\"goToDetail\">\n    <view class=\"house-image\">\n      <image \n        :src=\"house.thumbnail || '/static/default-house.png'\" \n        mode=\"aspectFill\"\n        class=\"image\"\n      />\n      <view class=\"image-count\" v-if=\"house.imageCount > 1\">\n        <text class=\"count-text\">{{ house.imageCount }}图</text>\n      </view>\n    </view>\n    \n    <view class=\"house-info\">\n      <view class=\"house-title\">{{ house.title }}</view>\n      \n      <view class=\"house-details\">\n        <text class=\"area\" v-if=\"house.area\">{{ house.area }}㎡</text>\n        <text class=\"floor\" v-if=\"house.floor\">{{ house.floor }}</text>\n        <text class=\"orientation\" v-if=\"house.orientation\">{{ house.orientation }}</text>\n      </view>\n      \n      <view class=\"house-tags\" v-if=\"house.tags && house.tags.length > 0\">\n        <text \n          class=\"tag\" \n          v-for=\"(tag, index) in house.tags.slice(0, 3)\" \n          :key=\"index\"\n        >\n          {{ tag }}\n        </text>\n      </view>\n      \n      <view class=\"house-address\">\n        <text class=\"address-text\">{{ house.address }}</text>\n      </view>\n      \n      <view class=\"house-footer\">\n        <view class=\"price-info\">\n          <text class=\"price\">¥{{ house.price }}</text>\n          <text class=\"price-unit\">/月</text>\n        </view>\n        \n        <view class=\"view-count\">\n          <text class=\"count\">{{ house.viewCount || 0 }}次浏览</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'HouseCard',\n  props: {\n    house: {\n      type: Object,\n      required: true\n    }\n  },\n  \n  methods: {\n    goToDetail() {\n      uni.navigateTo({\n        url: `/pages/detail/detail?id=${this.house._id}`\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.house-card {\n  background: #ffffff;\n  border-radius: 16rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.house-image {\n  position: relative;\n  width: 100%;\n  height: 400rpx;\n}\n\n.image {\n  width: 100%;\n  height: 100%;\n}\n\n.image-count {\n  position: absolute;\n  bottom: 16rpx;\n  right: 16rpx;\n  background: rgba(0, 0, 0, 0.6);\n  border-radius: 8rpx;\n  padding: 4rpx 12rpx;\n}\n\n.count-text {\n  color: #ffffff;\n  font-size: 24rpx;\n}\n\n.house-info {\n  padding: 24rpx;\n}\n\n.house-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333333;\n  margin-bottom: 16rpx;\n  line-height: 1.4;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.house-details {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.area, .floor, .orientation {\n  font-size: 26rpx;\n  color: #666666;\n  margin-right: 20rpx;\n}\n\n.house-tags {\n  display: flex;\n  flex-wrap: wrap;\n  margin-bottom: 16rpx;\n}\n\n.tag {\n  background: #f0f2ff;\n  color: #667eea;\n  font-size: 22rpx;\n  padding: 6rpx 12rpx;\n  border-radius: 6rpx;\n  margin-right: 12rpx;\n  margin-bottom: 8rpx;\n}\n\n.house-address {\n  margin-bottom: 20rpx;\n}\n\n.address-text {\n  font-size: 26rpx;\n  color: #999999;\n  line-height: 1.4;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.house-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.price-info {\n  display: flex;\n  align-items: baseline;\n}\n\n.price {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #ff6b6b;\n}\n\n.price-unit {\n  font-size: 24rpx;\n  color: #999999;\n  margin-left: 4rpx;\n}\n\n.view-count {\n  display: flex;\n  align-items: center;\n}\n\n.count {\n  font-size: 22rpx;\n  color: #cccccc;\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./house-card.vue?vue&type=style&index=0&id=529060b8&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./house-card.vue?vue&type=style&index=0&id=529060b8&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753838857903\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import { render, staticRenderFns, recyclableRender, components } from \"./house-card.vue?vue&type=template&id=529060b8&scoped=true&\"\nvar renderjs\nimport script from \"./house-card.vue?vue&type=script&lang=js&\"\nexport * from \"./house-card.vue?vue&type=script&lang=js&\"\nimport style0 from \"./house-card.vue?vue&type=style&index=0&id=529060b8&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"529060b8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/house-card/house-card.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./house-card.vue?vue&type=template&id=529060b8&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.house.tags && _vm.house.tags.length > 0\n  var l0 = g0 ? _vm.house.tags.slice(0, 3) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./house-card.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./house-card.vue?vue&type=script&lang=js&\""], "sourceRoot": ""}