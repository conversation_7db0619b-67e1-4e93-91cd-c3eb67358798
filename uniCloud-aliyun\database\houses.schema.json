{"bsonType": "object", "description": "房源信息表", "required": ["title", "price", "address"], "properties": {"_id": {"description": "房源唯一标识"}, "title": {"bsonType": "string", "description": "房源标题", "title": "房源标题", "trim": "both", "minLength": 1, "maxLength": 100}, "description": {"bsonType": "string", "description": "房源详细描述", "title": "房源描述", "maxLength": 2000}, "images": {"bsonType": "array", "description": "房源图片URL数组", "title": "房源图片", "items": {"bsonType": "string", "title": "图片URL"}}, "price": {"bsonType": "double", "description": "月租金（元）", "title": "月租金", "minimum": 0}, "address": {"bsonType": "string", "description": "房源地址", "title": "房源地址", "trim": "both", "minLength": 1, "maxLength": 200}, "tags": {"bsonType": "array", "description": "房源标签", "title": "房源标签", "items": {"bsonType": "string", "title": "标签"}}, "area": {"bsonType": "double", "description": "房屋面积（平方米）", "title": "房屋面积", "minimum": 0}, "floor": {"bsonType": "string", "description": "楼层信息", "title": "楼层"}, "orientation": {"bsonType": "string", "description": "房屋朝向", "title": "房屋朝向"}, "facilities": {"bsonType": "array", "description": "房屋设施", "title": "房屋设施", "items": {"bsonType": "string", "title": "设施"}}, "contact": {"bsonType": "object", "description": "联系方式", "title": "联系方式", "properties": {"name": {"bsonType": "string", "title": "联系人姓名"}, "phone": {"bsonType": "string", "title": "联系电话"}}}, "status": {"bsonType": "string", "description": "房源状态", "title": "房源状态", "enum": ["available", "unavailable", "rented"], "default": "available"}, "viewCount": {"bsonType": "int", "description": "浏览次数", "title": "浏览次数", "default": 0}, "createdAt": {"bsonType": "timestamp", "description": "创建时间", "title": "创建时间", "forceDefaultValue": {"$env": "now"}}, "updatedAt": {"bsonType": "timestamp", "description": "更新时间", "title": "更新时间", "forceDefaultValue": {"$env": "now"}}}, "indexes": [{"IndexName": "status_createdAt", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "status", "Direction": "1"}, {"Name": "createdAt", "Direction": "-1"}]}}]}