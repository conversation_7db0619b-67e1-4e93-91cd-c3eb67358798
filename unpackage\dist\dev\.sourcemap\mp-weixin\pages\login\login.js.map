{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/租房管理/pages/login/login.vue?db4c", "webpack:///D:/web/project/租房管理/pages/login/login.vue?7706", "webpack:///D:/web/project/租房管理/pages/login/login.vue?d4ea", "webpack:///D:/web/project/租房管理/pages/login/login.vue?0c93", "uni-app:///pages/login/login.vue", "webpack:///D:/web/project/租房管理/pages/login/login.vue?d71f", "webpack:///D:/web/project/租房管理/pages/login/login.vue?e974"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "formData", "username", "password", "loading", "methods", "validateForm", "uni", "title", "icon", "handleLogin", "uniCloud", "name", "result", "getApp", "setTimeout", "url", "console", "goToRegister"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACqC;;;AAGzF;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAgoB,CAAgB,8nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC8CppB;EACAC;IACA;MACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;QACAC;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIA;gBAAA;gBAAA;gBAAA,OAGAC;kBACAC;kBACAZ;oBACAE;oBACAC;kBACA;gBACA;cAAA;gBANAU;gBAQA;kBACA;kBAAA,sBACAA,gGAEA;kBACAN;kBACAA;;kBAEA;kBACAO;kBACAA;kBACAA;kBAEAP;oBACAC;oBACAC;kBACA;;kBAEA;kBACAM;oBACA;oBACA;oBACA;sBACAR;oBACA;sBACAA;wBACAS;sBACA;oBACA;kBACA;gBAEA;kBACAT;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAQ;gBACAV;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAS;MACAX;QACAS;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxKA;AAAA;AAAA;AAAA;AAAu7B,CAAgB,i5BAAG,EAAC,C;;;;;;;;;;;ACA38B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login/login.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/login/login.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login.vue?vue&type=template&id=b237504c&scoped=true&\"\nvar renderjs\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&id=b237504c&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b237504c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/login.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=template&id=b237504c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"login-container\">\n    <view class=\"login-header\">\n      <image class=\"logo\" src=\"/static/logo.png\" mode=\"aspectFit\"></image>\n      <text class=\"app-name\">毕业租房</text>\n      <text class=\"app-desc\">找到心仪的住所</text>\n    </view>\n    \n    <view class=\"login-form\">\n      <view class=\"form-item\">\n        <input \n          class=\"form-input\" \n          type=\"text\" \n          placeholder=\"请输入用户名/手机号\"\n          v-model=\"formData.username\"\n          :disabled=\"loading\"\n        />\n      </view>\n      \n      <view class=\"form-item\">\n        <input \n          class=\"form-input\" \n          type=\"password\" \n          placeholder=\"请输入密码\"\n          v-model=\"formData.password\"\n          :disabled=\"loading\"\n        />\n      </view>\n      \n      <button \n        class=\"login-btn\" \n        :class=\"{ 'login-btn-disabled': loading }\"\n        :disabled=\"loading\"\n        @click=\"handleLogin\"\n      >\n        {{ loading ? '登录中...' : '登录' }}\n      </button>\n      \n      <view class=\"form-footer\">\n        <text class=\"register-link\" @click=\"goToRegister\">还没有账号？立即注册</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      formData: {\n        username: '',\n        password: ''\n      },\n      loading: false\n    }\n  },\n  \n  methods: {\n    // 表单验证\n    validateForm() {\n      if (!this.formData.username.trim()) {\n        uni.showToast({\n          title: '请输入用户名',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      if (!this.formData.password.trim()) {\n        uni.showToast({\n          title: '请输入密码',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      if (this.formData.username.length < 3) {\n        uni.showToast({\n          title: '用户名长度不能少于3个字符',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      if (this.formData.password.length < 6) {\n        uni.showToast({\n          title: '密码长度不能少于6个字符',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      return true\n    },\n    \n    // 处理登录\n    async handleLogin() {\n      if (!this.validateForm()) {\n        return\n      }\n      \n      this.loading = true\n      \n      try {\n        const result = await uniCloud.callFunction({\n          name: 'loginUser',\n          data: {\n            username: this.formData.username.trim(),\n            password: this.formData.password.trim()\n          }\n        })\n        \n        if (result.result.code === 0) {\n          // 登录成功，保存用户信息和token\n          const { token, userInfo } = result.result.data\n          \n          // 保存到本地存储\n          uni.setStorageSync('token', token)\n          uni.setStorageSync('userInfo', userInfo)\n          \n          // 更新全局状态\n          getApp().globalData.token = token\n          getApp().globalData.userInfo = userInfo\n          getApp().globalData.isLogin = true\n          \n          uni.showToast({\n            title: '登录成功',\n            icon: 'success'\n          })\n          \n          // 延迟跳转，让用户看到成功提示\n          setTimeout(() => {\n            // 返回上一页或跳转到首页\n            const pages = getCurrentPages()\n            if (pages.length > 1) {\n              uni.navigateBack()\n            } else {\n              uni.reLaunch({\n                url: '/pages/index/index'\n              })\n            }\n          }, 1500)\n          \n        } else {\n          uni.showToast({\n            title: result.result.message || '登录失败',\n            icon: 'none'\n          })\n        }\n        \n      } catch (error) {\n        console.error('登录错误:', error)\n        uni.showToast({\n          title: '网络错误，请重试',\n          icon: 'none'\n        })\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 跳转到注册页面\n    goToRegister() {\n      uni.navigateTo({\n        url: '/pages/register/register'\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.login-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 0 60rpx;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n}\n\n.login-header {\n  text-align: center;\n  margin-bottom: 100rpx;\n}\n\n.logo {\n  width: 120rpx;\n  height: 120rpx;\n  margin-bottom: 30rpx;\n}\n\n.app-name {\n  display: block;\n  font-size: 48rpx;\n  font-weight: bold;\n  color: #ffffff;\n  margin-bottom: 20rpx;\n}\n\n.app-desc {\n  display: block;\n  font-size: 28rpx;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.login-form {\n  background: #ffffff;\n  border-radius: 20rpx;\n  padding: 60rpx 40rpx;\n  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);\n}\n\n.form-item {\n  margin-bottom: 40rpx;\n}\n\n.form-input {\n  width: 100%;\n  height: 88rpx;\n  background: #f8f9fa;\n  border: 2rpx solid #e9ecef;\n  border-radius: 12rpx;\n  padding: 0 30rpx;\n  font-size: 32rpx;\n  color: #333333;\n  box-sizing: border-box;\n}\n\n.form-input:focus {\n  border-color: #667eea;\n  background: #ffffff;\n}\n\n.login-btn {\n  width: 100%;\n  height: 88rpx;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: #ffffff;\n  border: none;\n  border-radius: 12rpx;\n  font-size: 32rpx;\n  font-weight: bold;\n  margin-top: 20rpx;\n}\n\n.login-btn-disabled {\n  opacity: 0.6;\n}\n\n.form-footer {\n  text-align: center;\n  margin-top: 40rpx;\n}\n\n.register-link {\n  color: #667eea;\n  font-size: 28rpx;\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&id=b237504c&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&id=b237504c&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753838857718\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}