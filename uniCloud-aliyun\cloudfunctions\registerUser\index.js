'use strict';

const { asyncWrapper, success, paramError, error } = require('response-util')
const { encryptPassword, generateToken } = require('auth-util')
const { create, exists } = require('db-util')

exports.main = asyncWrapper(async (event, context) => {
  const { username, password, email, phone, nickname } = event
  
  // 参数验证
  if (!username || !password) {
    return paramError('用户名和密码不能为空')
  }
  
  if (username.length < 3 || username.length > 20) {
    return paramError('用户名长度应在3-20个字符之间')
  }
  
  if (password.length < 6) {
    return paramError('密码长度不能少于6个字符')
  }
  
  // 验证用户名格式（可以是学号或手机号）
  const usernameRegex = /^[a-zA-Z0-9_]{3,20}$|^1[3-9]\d{9}$/
  if (!usernameRegex.test(username)) {
    return paramError('用户名格式不正确，请使用字母、数字、下划线或手机号')
  }
  
  // 验证邮箱格式（如果提供）
  if (email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return paramError('邮箱格式不正确')
    }
  }
  
  // 验证手机号格式（如果提供）
  if (phone) {
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!phoneRegex.test(phone)) {
      return paramError('手机号格式不正确')
    }
  }
  
  // 检查用户名是否已存在
  const userExists = await exists('users', { username })
  if (userExists) {
    return error('用户名已存在', 409)
  }
  
  // 检查邮箱是否已存在（如果提供）
  if (email) {
    const emailExists = await exists('users', { email })
    if (emailExists) {
      return error('邮箱已被注册', 409)
    }
  }
  
  // 检查手机号是否已存在（如果提供）
  if (phone) {
    const phoneExists = await exists('users', { phone })
    if (phoneExists) {
      return error('手机号已被注册', 409)
    }
  }
  
  // 加密密码
  const hashedPassword = encryptPassword(password)
  
  // 创建用户数据
  const userData = {
    username,
    password: hashedPassword,
    nickname: nickname || username,
    email: email || null,
    phone: phone || null,
    avatar: null,
    status: 0
  }
  
  // 创建用户
  const userId = await create('users', userData)
  
  // 生成token
  const token = await generateToken({
    uid: userId,
    username,
    role: ['user']
  })
  
  // 返回用户信息（不包含密码）
  const userInfo = {
    _id: userId,
    username,
    nickname: userData.nickname,
    email: userData.email,
    phone: userData.phone,
    avatar: userData.avatar
  }
  
  return success({
    token,
    userInfo
  }, '注册成功')
})
