
.house-card.data-v-529060b8 {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}
.house-image.data-v-529060b8 {
  position: relative;
  width: 100%;
  height: 400rpx;
}
.image.data-v-529060b8 {
  width: 100%;
  height: 100%;
}
.image-count.data-v-529060b8 {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 8rpx;
  padding: 4rpx 12rpx;
}
.count-text.data-v-529060b8 {
  color: #ffffff;
  font-size: 24rpx;
}
.house-info.data-v-529060b8 {
  padding: 24rpx;
}
.house-title.data-v-529060b8 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16rpx;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.house-details.data-v-529060b8 {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.area.data-v-529060b8, .floor.data-v-529060b8, .orientation.data-v-529060b8 {
  font-size: 26rpx;
  color: #666666;
  margin-right: 20rpx;
}
.house-tags.data-v-529060b8 {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
}
.tag.data-v-529060b8 {
  background: #f0f2ff;
  color: #667eea;
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
}
.house-address.data-v-529060b8 {
  margin-bottom: 20rpx;
}
.address-text.data-v-529060b8 {
  font-size: 26rpx;
  color: #999999;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.house-footer.data-v-529060b8 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.price-info.data-v-529060b8 {
  display: flex;
  align-items: baseline;
}
.price.data-v-529060b8 {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff6b6b;
}
.price-unit.data-v-529060b8 {
  font-size: 24rpx;
  color: #999999;
  margin-left: 4rpx;
}
.view-count.data-v-529060b8 {
  display: flex;
  align-items: center;
}
.count.data-v-529060b8 {
  font-size: 22rpx;
  color: #cccccc;
}

