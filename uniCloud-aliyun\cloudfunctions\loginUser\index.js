'use strict';

const { asyncWrapper, success, paramError, authError } = require('response-util')
const { verifyPassword, generateToken } = require('auth-util')
const { findOne } = require('db-util')

exports.main = asyncWrapper(async (event, context) => {
  const { username, password } = event
  
  // 参数验证
  if (!username || !password) {
    return paramError('用户名和密码不能为空')
  }
  
  if (username.length < 3 || username.length > 20) {
    return paramError('用户名长度应在3-20个字符之间')
  }
  
  if (password.length < 6) {
    return paramError('密码长度不能少于6个字符')
  }
  
  // 查找用户
  const user = await findOne('users', { username })
  
  if (!user) {
    return authError('用户名或密码错误')
  }
  
  // 检查用户状态
  if (user.status !== 0) {
    return authError('用户账户已被禁用')
  }
  
  // 验证密码
  const isPasswordValid = verifyPassword(password, user.password)
  if (!isPasswordValid) {
    return authError('用户名或密码错误')
  }
  
  // 生成token
  const token = await generateToken({
    uid: user._id,
    username: user.username,
    role: ['user']
  })
  
  // 返回用户信息（不包含密码）
  const userInfo = {
    _id: user._id,
    username: user.username,
    nickname: user.nickname,
    email: user.email,
    phone: user.phone,
    avatar: user.avatar,
    createdAt: user.createdAt
  }
  
  return success({
    token,
    userInfo
  }, '登录成功')
})
