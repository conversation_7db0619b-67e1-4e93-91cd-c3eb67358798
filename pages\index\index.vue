<template>
  <view class="container">
    <!-- 轮播图 -->
    <swiper class="banner" indicator-dots circular autoplay>
      <swiper-item v-for="(banner, index) in banners" :key="index">
        <image :src="banner.image" mode="aspectFill" class="banner-image" />
      </swiper-item>
    </swiper>

    <!-- 快捷导航 -->
    <view class="nav-grid">
      <view class="nav-item" @click="goToHouseList">
        <view class="nav-icon">🏠</view>
        <text class="nav-text">找房源</text>
      </view>
      <view class="nav-item" @click="goToFavorites">
        <view class="nav-icon">❤️</view>
        <text class="nav-text">我的收藏</text>
      </view>
      <view class="nav-item" @click="goToAppointments">
        <view class="nav-icon">📅</view>
        <text class="nav-text">我的预约</text>
      </view>
      <view class="nav-item" @click="goToPublish">
        <view class="nav-icon">📝</view>
        <text class="nav-text">发布房源</text>
      </view>
    </view>

    <!-- 推荐房源 -->
    <view class="section">
      <view class="section-header">
        <text class="section-title">推荐房源</text>
        <text class="section-more" @click="goToHouseList">查看更多 ></text>
      </view>

      <view class="house-grid">
        <view
          class="house-item"
          v-for="house in recommendHouses"
          :key="house._id"
          @click="goToDetail(house._id)"
        >
          <image
            :src="house.thumbnail || '/static/default-house.png'"
            mode="aspectFill"
            class="house-image"
          />
          <view class="house-info">
            <text class="house-title">{{ house.title }}</text>
            <text class="house-price">¥{{ house.price }}/月</text>
            <text class="house-address">{{ house.address }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 用户操作区 -->
    <view class="user-section" v-if="!isLogin">
      <view class="login-prompt">
        <text class="prompt-text">登录后享受更多服务</text>
        <button class="login-btn" @click="goToLogin">立即登录</button>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  data() {
    return {
      banners: [
        { image: '/static/banner1.jpg' },
        { image: '/static/banner2.jpg' },
        { image: '/static/banner3.jpg' }
      ],
      recommendHouses: []
    }
  },

  computed: {
    isLogin() {
      return getApp().globalData.isLogin
    }
  },

  onLoad() {
    this.loadRecommendHouses()
  },

  onShow() {
    // 页面显示时检查登录状态变化
    this.$forceUpdate()
  },

  methods: {
    // 加载推荐房源
    async loadRecommendHouses() {
      try {
        const result = await uniCloud.callFunction({
          name: 'listHouses',
          data: {
            page: 1,
            pageSize: 4,
            sortBy: 'viewCount',
            sortOrder: 'desc'
          }
        })

        if (result.result.code === 0) {
          this.recommendHouses = result.result.data.data
        }
      } catch (error) {
        console.error('加载推荐房源失败:', error)
      }
    },

    // 跳转到房源列表
    goToHouseList() {
      uni.navigateTo({
        url: '/pages/house-list/house-list'
      })
    },

    // 跳转到收藏页面
    goToFavorites() {
      if (!getApp().checkNeedLogin()) return
      uni.navigateTo({
        url: '/pages/favorites/favorites'
      })
    },

    // 跳转到预约页面
    goToAppointments() {
      if (!getApp().checkNeedLogin()) return
      uni.navigateTo({
        url: '/pages/appointments/appointments'
      })
    },

    // 跳转到发布房源
    goToPublish() {
      if (!getApp().checkNeedLogin()) return
      uni.navigateTo({
        url: '/pages/publish/publish'
      })
    },

    // 跳转到房源详情
    goToDetail(houseId) {
      uni.navigateTo({
        url: `/pages/detail/detail?id=${houseId}`
      })
    },

    // 跳转到登录页
    goToLogin() {
      uni.navigateTo({
        url: '/pages/login/login'
      })
    }
  }
}
</script>

<style scoped>
.container {
  background: #f5f5f5;
  min-height: 100vh;
}

/* 轮播图 */
.banner {
  width: 100%;
  height: 400rpx;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 快捷导航 */
.nav-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  background: #ffffff;
  padding: 40rpx 0;
  margin-bottom: 20rpx;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.nav-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.nav-text {
  font-size: 26rpx;
  color: #333333;
}

/* 推荐房源 */
.section {
  background: #ffffff;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.section-more {
  font-size: 26rpx;
  color: #667eea;
}

.house-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.house-item {
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.house-image {
  width: 100%;
  height: 240rpx;
}

.house-info {
  padding: 20rpx;
  background: #ffffff;
}

.house-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.house-price {
  font-size: 26rpx;
  color: #ff6b6b;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.house-address {
  font-size: 24rpx;
  color: #999999;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 用户操作区 */
.user-section {
  background: #ffffff;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
}

.login-prompt {
  text-align: center;
}

.prompt-text {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 30rpx;
  display: block;
}

.login-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
}
</style>
