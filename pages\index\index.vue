<template>
  <view class="container">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-input-wrapper">
        <input
          class="search-input"
          type="text"
          placeholder="搜索房源、地址..."
          v-model="searchKeyword"
          @confirm="handleSearch"
        />
        <view class="search-btn" @click="handleSearch">
          <text class="search-icon">🔍</text>
        </view>
      </view>
    </view>

    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view class="filter-item" @click="showSortModal">
        <text class="filter-text">{{ sortText }}</text>
        <text class="filter-arrow">▼</text>
      </view>

      <view class="filter-item" @click="showPriceModal">
        <text class="filter-text">{{ priceText }}</text>
        <text class="filter-arrow">▼</text>
      </view>

      <view class="user-actions">
        <view class="action-btn" @click="goToLogin" v-if="!isLogin">
          <text class="action-text">登录</text>
        </view>
        <view class="action-btn" @click="goToProfile" v-else>
          <text class="action-text">我的</text>
        </view>
      </view>
    </view>

    <!-- 房源列表 -->
    <scroll-view
      class="house-list"
      scroll-y
      @scrolltolower="loadMore"
      :refresher-enabled="true"
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <view class="list-content">
        <house-card
          v-for="house in houseList"
          :key="house._id"
          :house="house"
        />

        <!-- 加载状态 -->
        <view class="load-status" v-if="houseList.length > 0">
          <text class="status-text" v-if="loading">加载中...</text>
          <text class="status-text" v-else-if="noMore">没有更多了</text>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" v-if="!loading && houseList.length === 0">
          <text class="empty-text">暂无房源信息</text>
          <text class="empty-desc">请稍后再试或联系管理员</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>
<script>
import HouseCard from '@/components/house-card/house-card.vue'

export default {
  components: {
    HouseCard
  },

  data() {
    return {
      houseList: [],
      searchKeyword: '',
      currentPage: 1,
      pageSize: 10,
      loading: false,
      refreshing: false,
      noMore: false,

      // 筛选条件
      sortBy: 'createdAt',
      sortOrder: 'desc',
      minPrice: 0,
      maxPrice: 0
    }
  },

  computed: {
    isLogin() {
      return getApp().globalData.isLogin
    },

    sortText() {
      if (this.sortBy === 'price') {
        return this.sortOrder === 'asc' ? '价格升序' : '价格降序'
      } else if (this.sortBy === 'viewCount') {
        return '热度排序'
      } else {
        return '最新发布'
      }
    },

    priceText() {
      if (this.minPrice > 0 || this.maxPrice > 0) {
        if (this.minPrice > 0 && this.maxPrice > 0) {
          return `¥${this.minPrice}-${this.maxPrice}`
        } else if (this.minPrice > 0) {
          return `¥${this.minPrice}以上`
        } else {
          return `¥${this.maxPrice}以下`
        }
      }
      return '价格'
    }
  },

  onLoad() {
    this.loadHouseList()
  },

  onShow() {
    // 页面显示时检查登录状态变化
    this.$forceUpdate()
  },

  methods: {
    // 加载房源列表
    async loadHouseList(isRefresh = false) {
      if (this.loading) return

      this.loading = true

      if (isRefresh) {
        this.currentPage = 1
        this.noMore = false
      }

      try {
        const result = await uniCloud.callFunction({
          name: 'listHouses',
          data: {
            page: this.currentPage,
            pageSize: this.pageSize,
            keyword: this.searchKeyword,
            minPrice: this.minPrice,
            maxPrice: this.maxPrice,
            sortBy: this.sortBy,
            sortOrder: this.sortOrder
          }
        })

        if (result.result.code === 0) {
          const { data, totalPages } = result.result.data

          if (isRefresh) {
            this.houseList = data
          } else {
            this.houseList = [...this.houseList, ...data]
          }

          this.noMore = this.currentPage >= totalPages

          if (!this.noMore) {
            this.currentPage++
          }
        } else {
          uni.showToast({
            title: result.result.message || '加载失败',
            icon: 'none'
          })
        }

      } catch (error) {
        console.error('加载房源列表失败:', error)
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      } finally {
        this.loading = false
        this.refreshing = false
      }
    },

    // 搜索
    handleSearch() {
      this.loadHouseList(true)
    },

    // 下拉刷新
    onRefresh() {
      this.refreshing = true
      this.loadHouseList(true)
    },

    // 加载更多
    loadMore() {
      if (!this.noMore && !this.loading) {
        this.loadHouseList()
      }
    },

    // 显示排序选择
    showSortModal() {
      const items = ['最新发布', '价格升序', '价格降序', '热度排序']

      uni.showActionSheet({
        itemList: items,
        success: (res) => {
          const index = res.tapIndex
          switch (index) {
            case 0:
              this.sortBy = 'createdAt'
              this.sortOrder = 'desc'
              break
            case 1:
              this.sortBy = 'price'
              this.sortOrder = 'asc'
              break
            case 2:
              this.sortBy = 'price'
              this.sortOrder = 'desc'
              break
            case 3:
              this.sortBy = 'viewCount'
              this.sortOrder = 'desc'
              break
          }
          this.loadHouseList(true)
        }
      })
    },

    // 显示价格筛选
    showPriceModal() {
      const items = ['不限', '1000以下', '1000-2000', '2000-3000', '3000-5000', '5000以上']

      uni.showActionSheet({
        itemList: items,
        success: (res) => {
          const index = res.tapIndex
          switch (index) {
            case 0:
              this.minPrice = 0
              this.maxPrice = 0
              break
            case 1:
              this.minPrice = 0
              this.maxPrice = 1000
              break
            case 2:
              this.minPrice = 1000
              this.maxPrice = 2000
              break
            case 3:
              this.minPrice = 2000
              this.maxPrice = 3000
              break
            case 4:
              this.minPrice = 3000
              this.maxPrice = 5000
              break
            case 5:
              this.minPrice = 5000
              this.maxPrice = 0
              break
          }
          this.loadHouseList(true)
        }
      })
    },

    // 跳转到登录页
    goToLogin() {
      uni.navigateTo({
        url: '/pages/login/login'
      })
    },

    // 跳转到个人中心
    goToProfile() {
      uni.navigateTo({
        url: '/pages/profile/profile'
      })
    }
  }
}
</script>
