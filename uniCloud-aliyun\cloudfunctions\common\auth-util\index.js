/**
 * 用户认证工具类
 * 处理用户身份验证和权限检查
 */

const uniID = require('uni-id-common')

// 验证用户token并获取用户信息
async function verifyToken(token) {
  if (!token) {
    throw {
      code: 401,
      message: '缺少认证token'
    }
  }
  
  try {
    const result = uniID.checkToken(token)
    
    if (result.code !== 0) {
      throw {
        code: 401,
        message: result.msg || 'token验证失败'
      }
    }
    
    return result
  } catch (error) {
    console.error('token验证错误:', error)
    throw {
      code: 401,
      message: 'token验证失败'
    }
  }
}

// 从event中获取用户信息
async function getUserFromEvent(event) {
  const token = event.uniIdToken || event.token
  
  if (!token) {
    throw {
      code: 401,
      message: '用户未登录'
    }
  }
  
  const result = await verifyToken(token)
  return result.userInfo
}

// 检查用户是否有权限访问资源
function checkPermission(user, resource, action = 'read') {
  // 基础权限检查逻辑
  // 可以根据实际需求扩展
  
  if (!user) {
    throw {
      code: 401,
      message: '用户未登录'
    }
  }
  
  if (user.status !== 0) {
    throw {
      code: 403,
      message: '用户账户已被禁用'
    }
  }
  
  return true
}

// 检查用户是否可以访问指定用户的资源
function checkUserResourceAccess(currentUser, targetUserId) {
  if (!currentUser) {
    throw {
      code: 401,
      message: '用户未登录'
    }
  }
  
  if (currentUser.uid !== targetUserId) {
    throw {
      code: 403,
      message: '无权访问其他用户的资源'
    }
  }
  
  return true
}

// 密码加密
function encryptPassword(password) {
  return uniID.encryptPwd(password)
}

// 密码验证
function verifyPassword(password, hashedPassword) {
  return uniID.verifyPwd(password, hashedPassword)
}

// 生成token
async function generateToken(userInfo) {
  try {
    const result = await uniID.createToken({
      uid: userInfo._id || userInfo.uid,
      role: userInfo.role || ['user']
    })
    
    if (result.code !== 0) {
      throw new Error(result.msg || 'token生成失败')
    }
    
    return result.token
  } catch (error) {
    console.error('token生成错误:', error)
    throw {
      code: 500,
      message: 'token生成失败'
    }
  }
}

module.exports = {
  verifyToken,
  getUserFromEvent,
  checkPermission,
  checkUserResourceAccess,
  encryptPassword,
  verifyPassword,
  generateToken
}
