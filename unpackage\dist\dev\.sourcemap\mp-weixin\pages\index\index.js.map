{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/租房管理/pages/index/index.vue?5c05", "webpack:///D:/web/project/租房管理/pages/index/index.vue?8200", "webpack:///D:/web/project/租房管理/pages/index/index.vue?2cd3", "webpack:///D:/web/project/租房管理/pages/index/index.vue?4ad6", "uni-app:///pages/index/index.vue", "webpack:///D:/web/project/租房管理/pages/index/index.vue?6c51", "webpack:///D:/web/project/租房管理/pages/index/index.vue?8710"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "banners", "image", "recommendHouses", "computed", "is<PERSON>ogin", "onLoad", "onShow", "methods", "loadRecommendHouses", "uniCloud", "name", "page", "pageSize", "sortBy", "sortOrder", "result", "console", "goToHouseList", "uni", "url", "goToFavorites", "goToAppointments", "goToPublish", "goToDetail", "goToLogin"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACqC;;;AAGzF;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAgoB,CAAgB,8nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCmEppB;EACAC;IACA;MACAC,UACA;QAAAC;MAAA,GACA;QAAAA;MAAA,GACA;QAAAA;MAAA,EACA;MACAC;IACA;EACA;EAEAC;IACAC;MACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;kBACAC;kBACAX;oBACAY;oBACAC;oBACAC;oBACAC;kBACA;gBACA;cAAA;gBARAC;gBAUA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACAF;QACAC;MACA;IACA;IAEA;IACAE;MACA;MACAH;QACAC;MACA;IACA;IAEA;IACAG;MACA;MACAJ;QACAC;MACA;IACA;IAEA;IACAI;MACAL;QACAC;MACA;IACA;IAEA;IACAK;MACAN;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjKA;AAAA;AAAA;AAAA;AAAu7B,CAAgB,i5BAAG,EAAC,C;;;;;;;;;;;ACA38B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57280228\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"container\">\r\n    <!-- 轮播图 -->\r\n    <swiper class=\"banner\" indicator-dots circular autoplay>\r\n      <swiper-item v-for=\"(banner, index) in banners\" :key=\"index\">\r\n        <image :src=\"banner.image\" mode=\"aspectFill\" class=\"banner-image\" />\r\n      </swiper-item>\r\n    </swiper>\r\n\r\n    <!-- 快捷导航 -->\r\n    <view class=\"nav-grid\">\r\n      <view class=\"nav-item\" @click=\"goToHouseList\">\r\n        <view class=\"nav-icon\">🏠</view>\r\n        <text class=\"nav-text\">找房源</text>\r\n      </view>\r\n      <view class=\"nav-item\" @click=\"goToFavorites\">\r\n        <view class=\"nav-icon\">❤️</view>\r\n        <text class=\"nav-text\">我的收藏</text>\r\n      </view>\r\n      <view class=\"nav-item\" @click=\"goToAppointments\">\r\n        <view class=\"nav-icon\">📅</view>\r\n        <text class=\"nav-text\">我的预约</text>\r\n      </view>\r\n      <view class=\"nav-item\" @click=\"goToPublish\">\r\n        <view class=\"nav-icon\">📝</view>\r\n        <text class=\"nav-text\">发布房源</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 推荐房源 -->\r\n    <view class=\"section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">推荐房源</text>\r\n        <text class=\"section-more\" @click=\"goToHouseList\">查看更多 ></text>\r\n      </view>\r\n\r\n      <view class=\"house-grid\">\r\n        <view\r\n          class=\"house-item\"\r\n          v-for=\"house in recommendHouses\"\r\n          :key=\"house._id\"\r\n          @click=\"goToDetail(house._id)\"\r\n        >\r\n          <image\r\n            :src=\"house.thumbnail || '/static/default-house.png'\"\r\n            mode=\"aspectFill\"\r\n            class=\"house-image\"\r\n          />\r\n          <view class=\"house-info\">\r\n            <text class=\"house-title\">{{ house.title }}</text>\r\n            <text class=\"house-price\">¥{{ house.price }}/月</text>\r\n            <text class=\"house-address\">{{ house.address }}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 用户操作区 -->\r\n    <view class=\"user-section\" v-if=\"!isLogin\">\r\n      <view class=\"login-prompt\">\r\n        <text class=\"prompt-text\">登录后享受更多服务</text>\r\n        <button class=\"login-btn\" @click=\"goToLogin\">立即登录</button>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      banners: [\r\n        { image: '/static/banner1.jpg' },\r\n        { image: '/static/banner2.jpg' },\r\n        { image: '/static/banner3.jpg' }\r\n      ],\r\n      recommendHouses: []\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    isLogin() {\r\n      return getApp().globalData.isLogin\r\n    }\r\n  },\r\n\r\n  onLoad() {\r\n    this.loadRecommendHouses()\r\n  },\r\n\r\n  onShow() {\r\n    // 页面显示时检查登录状态变化\r\n    this.$forceUpdate()\r\n  },\r\n\r\n  methods: {\r\n    // 加载推荐房源\r\n    async loadRecommendHouses() {\r\n      try {\r\n        const result = await uniCloud.callFunction({\r\n          name: 'listHouses',\r\n          data: {\r\n            page: 1,\r\n            pageSize: 4,\r\n            sortBy: 'viewCount',\r\n            sortOrder: 'desc'\r\n          }\r\n        })\r\n\r\n        if (result.result.code === 0) {\r\n          this.recommendHouses = result.result.data.data\r\n        }\r\n      } catch (error) {\r\n        console.error('加载推荐房源失败:', error)\r\n      }\r\n    },\r\n\r\n    // 跳转到房源列表\r\n    goToHouseList() {\r\n      uni.navigateTo({\r\n        url: '/pages/house-list/house-list'\r\n      })\r\n    },\r\n\r\n    // 跳转到收藏页面\r\n    goToFavorites() {\r\n      if (!getApp().checkNeedLogin()) return\r\n      uni.navigateTo({\r\n        url: '/pages/favorites/favorites'\r\n      })\r\n    },\r\n\r\n    // 跳转到预约页面\r\n    goToAppointments() {\r\n      if (!getApp().checkNeedLogin()) return\r\n      uni.navigateTo({\r\n        url: '/pages/appointments/appointments'\r\n      })\r\n    },\r\n\r\n    // 跳转到发布房源\r\n    goToPublish() {\r\n      if (!getApp().checkNeedLogin()) return\r\n      uni.navigateTo({\r\n        url: '/pages/publish/publish'\r\n      })\r\n    },\r\n\r\n    // 跳转到房源详情\r\n    goToDetail(houseId) {\r\n      uni.navigateTo({\r\n        url: `/pages/detail/detail?id=${houseId}`\r\n      })\r\n    },\r\n\r\n    // 跳转到登录页\r\n    goToLogin() {\r\n      uni.navigateTo({\r\n        url: '/pages/login/login'\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.container {\r\n  background: #f5f5f5;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 轮播图 */\r\n.banner {\r\n  width: 100%;\r\n  height: 400rpx;\r\n}\r\n\r\n.banner-image {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n/* 快捷导航 */\r\n.nav-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(4, 1fr);\r\n  background: #ffffff;\r\n  padding: 40rpx 0;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.nav-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.nav-icon {\r\n  font-size: 48rpx;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.nav-text {\r\n  font-size: 26rpx;\r\n  color: #333333;\r\n}\r\n\r\n/* 推荐房源 */\r\n.section {\r\n  background: #ffffff;\r\n  margin-bottom: 20rpx;\r\n  padding: 30rpx;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.section-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #333333;\r\n}\r\n\r\n.section-more {\r\n  font-size: 26rpx;\r\n  color: #667eea;\r\n}\r\n\r\n.house-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 20rpx;\r\n}\r\n\r\n.house-item {\r\n  border-radius: 12rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.house-image {\r\n  width: 100%;\r\n  height: 240rpx;\r\n}\r\n\r\n.house-info {\r\n  padding: 20rpx;\r\n  background: #ffffff;\r\n}\r\n\r\n.house-title {\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  color: #333333;\r\n  margin-bottom: 8rpx;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.house-price {\r\n  font-size: 26rpx;\r\n  color: #ff6b6b;\r\n  font-weight: bold;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.house-address {\r\n  font-size: 24rpx;\r\n  color: #999999;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 用户操作区 */\r\n.user-section {\r\n  background: #ffffff;\r\n  padding: 40rpx 30rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.login-prompt {\r\n  text-align: center;\r\n}\r\n\r\n.prompt-text {\r\n  font-size: 28rpx;\r\n  color: #666666;\r\n  margin-bottom: 30rpx;\r\n  display: block;\r\n}\r\n\r\n.login-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: #ffffff;\r\n  border: none;\r\n  border-radius: 50rpx;\r\n  padding: 20rpx 60rpx;\r\n  font-size: 28rpx;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753838857733\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}