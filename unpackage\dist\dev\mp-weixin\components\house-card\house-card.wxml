<view data-event-opts="{{[['tap',[['goToDetail',['$event']]]]]}}" class="house-card data-v-529060b8" bindtap="__e"><view class="house-image data-v-529060b8"><image class="image data-v-529060b8" src="{{house.thumbnail||'/static/default-house.png'}}" mode="aspectFill"></image><block wx:if="{{house.imageCount>1}}"><view class="image-count data-v-529060b8"><text class="count-text data-v-529060b8">{{house.imageCount+"图"}}</text></view></block></view><view class="house-info data-v-529060b8"><view class="house-title data-v-529060b8">{{house.title}}</view><view class="house-details data-v-529060b8"><block wx:if="{{house.area}}"><text class="area data-v-529060b8">{{house.area+"㎡"}}</text></block><block wx:if="{{house.floor}}"><text class="floor data-v-529060b8">{{house.floor}}</text></block><block wx:if="{{house.orientation}}"><text class="orientation data-v-529060b8">{{house.orientation}}</text></block></view><block wx:if="{{$root.g0}}"><view class="house-tags data-v-529060b8"><block wx:for="{{$root.l0}}" wx:for-item="tag" wx:for-index="index" wx:key="index"><text class="tag data-v-529060b8">{{''+tag+''}}</text></block></view></block><view class="house-address data-v-529060b8"><text class="address-text data-v-529060b8">{{house.address}}</text></view><view class="house-footer data-v-529060b8"><view class="price-info data-v-529060b8"><text class="price data-v-529060b8">{{"¥"+house.price}}</text><text class="price-unit data-v-529060b8">/月</text></view><view class="view-count data-v-529060b8"><text class="count data-v-529060b8">{{(house.viewCount||0)+"次浏览"}}</text></view></view></view></view>