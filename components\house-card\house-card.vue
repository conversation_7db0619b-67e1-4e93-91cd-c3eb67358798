<template>
  <view class="house-card" @click="goToDetail">
    <view class="house-image">
      <image 
        :src="house.thumbnail || '/static/default-house.png'" 
        mode="aspectFill"
        class="image"
      />
      <view class="image-count" v-if="house.imageCount > 1">
        <text class="count-text">{{ house.imageCount }}图</text>
      </view>
    </view>
    
    <view class="house-info">
      <view class="house-title">{{ house.title }}</view>
      
      <view class="house-details">
        <text class="area" v-if="house.area">{{ house.area }}㎡</text>
        <text class="floor" v-if="house.floor">{{ house.floor }}</text>
        <text class="orientation" v-if="house.orientation">{{ house.orientation }}</text>
      </view>
      
      <view class="house-tags" v-if="house.tags && house.tags.length > 0">
        <text 
          class="tag" 
          v-for="(tag, index) in house.tags.slice(0, 3)" 
          :key="index"
        >
          {{ tag }}
        </text>
      </view>
      
      <view class="house-address">
        <text class="address-text">{{ house.address }}</text>
      </view>
      
      <view class="house-footer">
        <view class="price-info">
          <text class="price">¥{{ house.price }}</text>
          <text class="price-unit">/月</text>
        </view>
        
        <view class="view-count">
          <text class="count">{{ house.viewCount || 0 }}次浏览</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'HouseCard',
  props: {
    house: {
      type: Object,
      required: true
    }
  },
  
  methods: {
    goToDetail() {
      uni.navigateTo({
        url: `/pages/detail/detail?id=${this.house._id}`
      })
    }
  }
}
</script>

<style scoped>
.house-card {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.house-image {
  position: relative;
  width: 100%;
  height: 400rpx;
}

.image {
  width: 100%;
  height: 100%;
}

.image-count {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 8rpx;
  padding: 4rpx 12rpx;
}

.count-text {
  color: #ffffff;
  font-size: 24rpx;
}

.house-info {
  padding: 24rpx;
}

.house-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16rpx;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.house-details {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.area, .floor, .orientation {
  font-size: 26rpx;
  color: #666666;
  margin-right: 20rpx;
}

.house-tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
}

.tag {
  background: #f0f2ff;
  color: #667eea;
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
}

.house-address {
  margin-bottom: 20rpx;
}

.address-text {
  font-size: 26rpx;
  color: #999999;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.house-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-info {
  display: flex;
  align-items: baseline;
}

.price {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff6b6b;
}

.price-unit {
  font-size: 24rpx;
  color: #999999;
  margin-left: 4rpx;
}

.view-count {
  display: flex;
  align-items: center;
}

.count {
  font-size: 22rpx;
  color: #cccccc;
}
</style>
