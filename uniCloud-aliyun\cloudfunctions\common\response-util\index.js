/**
 * 云函数响应工具类
 * 统一响应格式和错误处理
 */

// 成功响应
function success(data = null, message = 'success') {
  return {
    code: 0,
    message,
    data,
    timestamp: Date.now()
  }
}

// 错误响应
function error(message = 'error', code = 1, data = null) {
  return {
    code,
    message,
    data,
    timestamp: Date.now()
  }
}

// 参数验证错误
function paramError(message = '参数错误') {
  return error(message, 400)
}

// 认证错误
function authError(message = '认证失败') {
  return error(message, 401)
}

// 权限错误
function permissionError(message = '权限不足') {
  return error(message, 403)
}

// 资源不存在错误
function notFoundError(message = '资源不存在') {
  return error(message, 404)
}

// 服务器内部错误
function serverError(message = '服务器内部错误') {
  return error(message, 500)
}

// 数据库操作错误处理
function handleDbError(err) {
  console.error('数据库操作错误:', err)
  
  if (err.code) {
    switch (err.code) {
      case 11000:
        return error('数据已存在', 409)
      case 121:
        return paramError('数据格式不正确')
      default:
        return serverError('数据库操作失败')
    }
  }
  
  return serverError(err.message || '数据库操作失败')
}

// 异步函数错误包装器
function asyncWrapper(fn) {
  return async (event, context) => {
    try {
      return await fn(event, context)
    } catch (err) {
      console.error('云函数执行错误:', err)
      
      // 如果是已知的响应格式，直接返回
      if (err.code !== undefined) {
        return err
      }
      
      // 处理数据库错误
      if (err.name === 'MongoError' || err.name === 'MongoServerError') {
        return handleDbError(err)
      }
      
      // 其他未知错误
      return serverError(err.message || '未知错误')
    }
  }
}

module.exports = {
  success,
  error,
  paramError,
  authError,
  permissionError,
  notFoundError,
  serverError,
  handleDbError,
  asyncWrapper
}
