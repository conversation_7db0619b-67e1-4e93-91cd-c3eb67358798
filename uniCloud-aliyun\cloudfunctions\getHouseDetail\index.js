'use strict';

const { asyncWrapper, success, paramError, notFoundError } = require('response-util')
const { findById, updateById, findOne } = require('db-util')
const { getUserFromEvent } = require('auth-util')

exports.main = asyncWrapper(async (event, context) => {
  const { houseId } = event
  
  // 参数验证
  if (!houseId) {
    return paramError('房源ID不能为空')
  }
  
  try {
    // 查询房源详情
    const house = await findById('houses', houseId)
    
    if (!house) {
      return notFoundError('房源不存在')
    }
    
    // 检查房源状态
    if (house.status !== 'available') {
      return notFoundError('房源已下架')
    }
    
    // 增加浏览次数
    await updateById('houses', houseId, {
      viewCount: (house.viewCount || 0) + 1
    })
    
    // 检查用户是否已收藏（如果用户已登录）
    let isFavorited = false
    try {
      const user = await getUserFromEvent(event)
      if (user) {
        const favorite = await findOne('favorites', {
          userId: user.uid,
          houseId: houseId
        })
        isFavorited = !!favorite
      }
    } catch (error) {
      // 用户未登录，忽略错误
      console.log('用户未登录，跳过收藏状态检查')
    }
    
    // 返回完整房源信息
    const result = {
      ...house,
      viewCount: (house.viewCount || 0) + 1, // 返回更新后的浏览次数
      isFavorited
    }
    
    return success(result, '获取房源详情成功')
    
  } catch (error) {
    console.error('获取房源详情失败:', error)
    throw error
  }
})
