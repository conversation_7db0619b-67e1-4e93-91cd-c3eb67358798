'use strict';

const { asyncWrapper, validateAuth, createResponse } = require('utils');

exports.main = asyncWrapper(async (event, context) => {
	// 验证用户身份
	const userInfo = await validateAuth(context);
	
	const { 
		title, 
		description, 
		price, 
		area, 
		type, 
		address, 
		contact, 
		tags = [], 
		images = [], 
		facilities = [] 
	} = event;
	
	// 参数验证
	if (!title || !description || !price || !area || !type || !address || !contact) {
		return createResponse(false, '请填写完整的房源信息');
	}
	
	if (price <= 0 || area <= 0) {
		return createResponse(false, '价格和面积必须大于0');
	}
	
	// 获取数据库引用
	const db = uniCloud.database();
	const housesCollection = db.collection('houses');
	
	// 构建房源数据
	const houseData = {
		title: title.trim(),
		description: description.trim(),
		price: Number(price),
		area: Number(area),
		type,
		address: address.trim(),
		contact: contact.trim(),
		tags: Array.isArray(tags) ? tags : [],
		images: Array.isArray(images) ? images : [],
		facilities: Array.isArray(facilities) ? facilities : [],
		thumbnail: images.length > 0 ? images[0] : '', // 第一张图片作为缩略图
		ownerId: userInfo.uid,
		ownerName: userInfo.nickname || userInfo.username || '匿名用户',
		status: 'available', // available, rented, offline
		viewCount: 0,
		favoriteCount: 0,
		createdAt: new Date(),
		updatedAt: new Date()
	};
	
	try {
		// 插入房源数据
		const result = await housesCollection.add(houseData);
		
		if (result.id) {
			return createResponse(true, '房源发布成功', {
				houseId: result.id
			});
		} else {
			return createResponse(false, '房源发布失败');
		}
	} catch (error) {
		console.error('发布房源失败:', error);
		return createResponse(false, '房源发布失败，请重试');
	}
});
