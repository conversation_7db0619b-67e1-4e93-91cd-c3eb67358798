
.container.data-v-177a8126 {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 20rpx;
}
.form-container.data-v-177a8126 {
	background: white;
	border-radius: 20rpx;
	padding: 40rpx;
	margin: 20rpx 0;
}
.form-title.data-v-177a8126 {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
	text-align: center;
	margin-bottom: 60rpx;
}
.form-item.data-v-177a8126 {
	margin-bottom: 40rpx;
}
.label.data-v-177a8126 {
	display: block;
	font-size: 32rpx;
	color: #333;
	margin-bottom: 20rpx;
	font-weight: 500;
}
.input.data-v-177a8126, .textarea.data-v-177a8126 {
	width: 100%;
	padding: 24rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	font-size: 30rpx;
	background: #fafafa;
}
.textarea.data-v-177a8126 {
	height: 200rpx;
	resize: none;
}
.price-input.data-v-177a8126, .area-input.data-v-177a8126 {
	display: flex;
	align-items: center;
}
.price-input .input.data-v-177a8126, .area-input .input.data-v-177a8126 {
	flex: 1;
	margin-right: 20rpx;
}
.price-unit.data-v-177a8126, .area-unit.data-v-177a8126 {
	font-size: 28rpx;
	color: #666;
}
.picker.data-v-177a8126 {
	padding: 24rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	background: #fafafa;
	font-size: 30rpx;
	color: #333;
}
.tags-container.data-v-177a8126, .facilities-container.data-v-177a8126 {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}
.tag-item.data-v-177a8126, .facility-item.data-v-177a8126 {
	padding: 16rpx 32rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 40rpx;
	font-size: 28rpx;
	color: #666;
	background: #fafafa;
}
.tag-item.active.data-v-177a8126, .facility-item.active.data-v-177a8126 {
	background: #667eea;
	color: white;
	border-color: #667eea;
}
.image-upload.data-v-177a8126 {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}
.image-item.data-v-177a8126 {
	position: relative;
	width: 200rpx;
	height: 200rpx;
}
.uploaded-image.data-v-177a8126 {
	width: 100%;
	height: 100%;
	border-radius: 12rpx;
}
.delete-btn.data-v-177a8126 {
	position: absolute;
	top: -10rpx;
	right: -10rpx;
	width: 40rpx;
	height: 40rpx;
	background: #ff4757;
	color: white;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
}
.upload-btn.data-v-177a8126 {
	width: 200rpx;
	height: 200rpx;
	border: 2rpx dashed #ccc;
	border-radius: 12rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background: #fafafa;
}
.upload-text.data-v-177a8126 {
	font-size: 60rpx;
	color: #ccc;
}
.upload-tip.data-v-177a8126 {
	font-size: 24rpx;
	color: #999;
	margin-top: 10rpx;
}
.tip.data-v-177a8126 {
	font-size: 24rpx;
	color: #999;
	margin-top: 10rpx;
}
.submit-container.data-v-177a8126 {
	margin-top: 60rpx;
}
.submit-btn.data-v-177a8126 {
	width: 100%;
	height: 88rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 44rpx;
	font-size: 32rpx;
	font-weight: bold;
}
.submit-btn.data-v-177a8126:disabled {
	opacity: 0.6;
}

