<view class="register-container data-v-891c2434"><view class="register-header data-v-891c2434"><text class="page-title data-v-891c2434">注册账号</text><text class="page-desc data-v-891c2434">加入毕业租房，开始找房之旅</text></view><view class="register-form data-v-891c2434"><view class="form-item data-v-891c2434"><input class="form-input data-v-891c2434" type="text" placeholder="请输入用户名（3-20个字符）" disabled="{{loading}}" data-event-opts="{{[['input',[['__set_model',['$0','username','$event',[]],['formData']]]]]}}" value="{{formData.username}}" bindinput="__e"/></view><view class="form-item data-v-891c2434"><input class="form-input data-v-891c2434" type="password" placeholder="请输入密码（至少6个字符）" disabled="{{loading}}" data-event-opts="{{[['input',[['__set_model',['$0','password','$event',[]],['formData']]]]]}}" value="{{formData.password}}" bindinput="__e"/></view><view class="form-item data-v-891c2434"><input class="form-input data-v-891c2434" type="password" placeholder="请确认密码" disabled="{{loading}}" data-event-opts="{{[['input',[['__set_model',['$0','confirmPassword','$event',[]],['formData']]]]]}}" value="{{formData.confirmPassword}}" bindinput="__e"/></view><view class="form-item data-v-891c2434"><input class="form-input data-v-891c2434" type="text" placeholder="请输入昵称（可选）" disabled="{{loading}}" data-event-opts="{{[['input',[['__set_model',['$0','nickname','$event',[]],['formData']]]]]}}" value="{{formData.nickname}}" bindinput="__e"/></view><view class="form-item data-v-891c2434"><input class="form-input data-v-891c2434" type="text" placeholder="请输入手机号（可选）" disabled="{{loading}}" data-event-opts="{{[['input',[['__set_model',['$0','phone','$event',[]],['formData']]]]]}}" value="{{formData.phone}}" bindinput="__e"/></view><view class="form-item data-v-891c2434"><input class="form-input data-v-891c2434" type="text" placeholder="请输入邮箱（可选）" disabled="{{loading}}" data-event-opts="{{[['input',[['__set_model',['$0','email','$event',[]],['formData']]]]]}}" value="{{formData.email}}" bindinput="__e"/></view><button class="{{['register-btn','data-v-891c2434',(loading)?'register-btn-disabled':'']}}" disabled="{{loading}}" data-event-opts="{{[['tap',[['handleRegister',['$event']]]]]}}" bindtap="__e">{{''+(loading?'注册中...':'注册')+''}}</button><view class="form-footer data-v-891c2434"><text data-event-opts="{{[['tap',[['goToLogin',['$event']]]]]}}" class="login-link data-v-891c2434" bindtap="__e">已有账号？立即登录</text></view></view></view>