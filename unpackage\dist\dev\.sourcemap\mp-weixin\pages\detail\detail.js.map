{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/租房管理/pages/detail/detail.vue?f182", "webpack:///D:/web/project/租房管理/pages/detail/detail.vue?5147", "webpack:///D:/web/project/租房管理/pages/detail/detail.vue?e9d2", "webpack:///D:/web/project/租房管理/pages/detail/detail.vue?e917", "uni-app:///pages/detail/detail.vue", "webpack:///D:/web/project/租房管理/pages/detail/detail.vue?7efb", "webpack:///D:/web/project/租房管理/pages/detail/detail.vue?16b6"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "houseId", "houseDetail", "loading", "onLoad", "methods", "loadHouseDetail", "uniCloud", "name", "result", "uni", "title", "icon", "console", "toggleFavorite", "getApp", "contactOwner", "phoneNumber", "makeAppointment", "url", "formatDate", "goBack"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACqC;;;AAG1F;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzBA;AAAA;AAAA;AAAA;AAAioB,CAAgB,+nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC6GrpB;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;MACA;MACA;IACA;MACA;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA;gBAAA,OAGAC;kBACAC;kBACAR;oBACAC;kBACA;gBACA;cAAA;gBALAQ;gBAOA;kBACA;gBACA;kBACAC;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;gBACAH;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACAC;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGAR;kBACAC;kBACAR;oBACAC;kBACA;gBACA;cAAA;gBALAQ;gBAOA;kBACA;kBACAC;oBACAC;oBACAC;kBACA;gBACA;kBACAF;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;gBACAH;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAI;MACA;QACAN;UACAC;UACAC;QACA;QACA;MACA;MAEAF;QACAO;MACA;IACA;IAEA;IACAC;MACA;MAEAR;QACAS;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACAX;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtOA;AAAA;AAAA;AAAA;AAAw7B,CAAgB,k5BAAG,EAAC,C;;;;;;;;;;;ACA58B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/detail/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/detail/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=3e159eb4&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&id=3e159eb4&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3e159eb4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/detail/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=3e159eb4&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.houseDetail\n    ? _vm.houseDetail.tags && _vm.houseDetail.tags.length > 0\n    : null\n  var g1 = _vm.houseDetail\n    ? _vm.houseDetail.facilities && _vm.houseDetail.facilities.length > 0\n    : null\n  var m0 = _vm.houseDetail ? _vm.formatDate(_vm.houseDetail.createdAt) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\" v-if=\"houseDetail\">\n    <!-- 图片轮播 -->\n    <swiper class=\"image-swiper\" indicator-dots circular>\n      <swiper-item v-for=\"(image, index) in houseDetail.images\" :key=\"index\">\n        <image :src=\"image\" mode=\"aspectFill\" class=\"house-image\" />\n      </swiper-item>\n    </swiper>\n\n    <!-- 房源基本信息 -->\n    <view class=\"house-info\">\n      <view class=\"house-title\">{{ houseDetail.title }}</view>\n\n      <view class=\"price-info\">\n        <text class=\"price\">¥{{ houseDetail.price }}</text>\n        <text class=\"price-unit\">/月</text>\n      </view>\n\n      <view class=\"house-details\">\n        <view class=\"detail-item\" v-if=\"houseDetail.area\">\n          <text class=\"detail-label\">面积：</text>\n          <text class=\"detail-value\">{{ houseDetail.area }}㎡</text>\n        </view>\n        <view class=\"detail-item\" v-if=\"houseDetail.floor\">\n          <text class=\"detail-label\">楼层：</text>\n          <text class=\"detail-value\">{{ houseDetail.floor }}</text>\n        </view>\n        <view class=\"detail-item\" v-if=\"houseDetail.orientation\">\n          <text class=\"detail-label\">朝向：</text>\n          <text class=\"detail-value\">{{ houseDetail.orientation }}</text>\n        </view>\n      </view>\n\n      <view class=\"house-tags\" v-if=\"houseDetail.tags && houseDetail.tags.length > 0\">\n        <text class=\"tag\" v-for=\"(tag, index) in houseDetail.tags\" :key=\"index\">\n          {{ tag }}\n        </text>\n      </view>\n    </view>\n\n    <!-- 房源描述 -->\n    <view class=\"section\">\n      <view class=\"section-title\">房源描述</view>\n      <text class=\"description\">{{ houseDetail.description || '暂无描述' }}</text>\n    </view>\n\n    <!-- 配套设施 -->\n    <view class=\"section\" v-if=\"houseDetail.facilities && houseDetail.facilities.length > 0\">\n      <view class=\"section-title\">配套设施</view>\n      <view class=\"facilities\">\n        <view class=\"facility-item\" v-for=\"(facility, index) in houseDetail.facilities\" :key=\"index\">\n          <text class=\"facility-text\">{{ facility }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 位置信息 -->\n    <view class=\"section\">\n      <view class=\"section-title\">位置信息</view>\n      <text class=\"address\">{{ houseDetail.address }}</text>\n    </view>\n\n    <!-- 联系信息 -->\n    <view class=\"section\" v-if=\"houseDetail.contact\">\n      <view class=\"section-title\">联系方式</view>\n      <view class=\"contact-info\">\n        <text class=\"contact-name\">{{ houseDetail.contact.name || '房东' }}</text>\n        <text class=\"contact-phone\">{{ houseDetail.contact.phone }}</text>\n      </view>\n    </view>\n\n    <!-- 浏览统计 -->\n    <view class=\"stats\">\n      <text class=\"stats-text\">{{ houseDetail.viewCount || 0 }}人浏览过</text>\n      <text class=\"stats-text\">发布于{{ formatDate(houseDetail.createdAt) }}</text>\n    </view>\n\n    <!-- 底部操作栏 -->\n    <view class=\"bottom-actions\">\n      <view class=\"action-btn favorite-btn\" @click=\"toggleFavorite\">\n        <text class=\"action-icon\">{{ houseDetail.isFavorited ? '❤️' : '🤍' }}</text>\n        <text class=\"action-text\">{{ houseDetail.isFavorited ? '已收藏' : '收藏' }}</text>\n      </view>\n\n      <view class=\"action-btn contact-btn\" @click=\"contactOwner\">\n        <text class=\"action-icon\">📞</text>\n        <text class=\"action-text\">联系房东</text>\n      </view>\n\n      <view class=\"action-btn appointment-btn\" @click=\"makeAppointment\">\n        <text class=\"action-icon\">📅</text>\n        <text class=\"action-text\">预约看房</text>\n      </view>\n    </view>\n  </view>\n\n  <!-- 加载状态 -->\n  <view class=\"loading\" v-else-if=\"loading\">\n    <text class=\"loading-text\">加载中...</text>\n  </view>\n\n  <!-- 错误状态 -->\n  <view class=\"error\" v-else>\n    <text class=\"error-text\">房源不存在或已下架</text>\n    <button class=\"back-btn\" @click=\"goBack\">返回</button>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      houseId: '',\n      houseDetail: null,\n      loading: true\n    }\n  },\n\n  onLoad(options) {\n    if (options.id) {\n      this.houseId = options.id\n      this.loadHouseDetail()\n    } else {\n      this.loading = false\n    }\n  },\n\n  methods: {\n    // 加载房源详情\n    async loadHouseDetail() {\n      this.loading = true\n\n      try {\n        const result = await uniCloud.callFunction({\n          name: 'getHouseDetail',\n          data: {\n            houseId: this.houseId\n          }\n        })\n\n        if (result.result.code === 0) {\n          this.houseDetail = result.result.data\n        } else {\n          uni.showToast({\n            title: result.result.message || '加载失败',\n            icon: 'none'\n          })\n        }\n\n      } catch (error) {\n        console.error('加载房源详情失败:', error)\n        uni.showToast({\n          title: '网络错误，请重试',\n          icon: 'none'\n        })\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 切换收藏状态\n    async toggleFavorite() {\n      if (!getApp().checkNeedLogin()) return\n\n      try {\n        const result = await uniCloud.callFunction({\n          name: 'toggleFavorite',\n          data: {\n            houseId: this.houseId\n          }\n        })\n\n        if (result.result.code === 0) {\n          this.houseDetail.isFavorited = !this.houseDetail.isFavorited\n          uni.showToast({\n            title: this.houseDetail.isFavorited ? '收藏成功' : '取消收藏',\n            icon: 'success'\n          })\n        } else {\n          uni.showToast({\n            title: result.result.message || '操作失败',\n            icon: 'none'\n          })\n        }\n\n      } catch (error) {\n        console.error('切换收藏状态失败:', error)\n        uni.showToast({\n          title: '网络错误，请重试',\n          icon: 'none'\n        })\n      }\n    },\n\n    // 联系房东\n    contactOwner() {\n      if (!this.houseDetail.contact || !this.houseDetail.contact.phone) {\n        uni.showToast({\n          title: '暂无联系方式',\n          icon: 'none'\n        })\n        return\n      }\n\n      uni.makePhoneCall({\n        phoneNumber: this.houseDetail.contact.phone\n      })\n    },\n\n    // 预约看房\n    makeAppointment() {\n      if (!getApp().checkNeedLogin()) return\n\n      uni.navigateTo({\n        url: `/pages/appointment/appointment?houseId=${this.houseId}`\n      })\n    },\n\n    // 格式化日期\n    formatDate(dateStr) {\n      if (!dateStr) return ''\n      const date = new Date(dateStr)\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`\n    },\n\n    // 返回上一页\n    goBack() {\n      uni.navigateBack()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.container {\n  background: #f5f5f5;\n  min-height: 100vh;\n  padding-bottom: 120rpx;\n}\n\n/* 图片轮播 */\n.image-swiper {\n  width: 100%;\n  height: 500rpx;\n}\n\n.house-image {\n  width: 100%;\n  height: 100%;\n}\n\n/* 房源基本信息 */\n.house-info {\n  background: #ffffff;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n}\n\n.house-title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #333333;\n  margin-bottom: 20rpx;\n  line-height: 1.4;\n}\n\n.price-info {\n  display: flex;\n  align-items: baseline;\n  margin-bottom: 20rpx;\n}\n\n.price {\n  font-size: 48rpx;\n  font-weight: bold;\n  color: #ff6b6b;\n}\n\n.price-unit {\n  font-size: 28rpx;\n  color: #999999;\n  margin-left: 8rpx;\n}\n\n.house-details {\n  display: flex;\n  flex-wrap: wrap;\n  margin-bottom: 20rpx;\n}\n\n.detail-item {\n  display: flex;\n  align-items: center;\n  margin-right: 40rpx;\n  margin-bottom: 10rpx;\n}\n\n.detail-label {\n  font-size: 26rpx;\n  color: #666666;\n}\n\n.detail-value {\n  font-size: 26rpx;\n  color: #333333;\n  font-weight: 500;\n}\n\n.house-tags {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.tag {\n  background: #f0f2ff;\n  color: #667eea;\n  font-size: 24rpx;\n  padding: 8rpx 16rpx;\n  border-radius: 8rpx;\n  margin-right: 16rpx;\n  margin-bottom: 10rpx;\n}\n\n/* 通用区块 */\n.section {\n  background: #ffffff;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333333;\n  margin-bottom: 20rpx;\n}\n\n.description {\n  font-size: 28rpx;\n  color: #666666;\n  line-height: 1.6;\n}\n\n/* 配套设施 */\n.facilities {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.facility-item {\n  background: #f8f9fa;\n  border-radius: 8rpx;\n  padding: 12rpx 20rpx;\n  margin-right: 16rpx;\n  margin-bottom: 16rpx;\n}\n\n.facility-text {\n  font-size: 26rpx;\n  color: #333333;\n}\n\n/* 位置和联系信息 */\n.address {\n  font-size: 28rpx;\n  color: #333333;\n  line-height: 1.5;\n}\n\n.contact-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.contact-name {\n  font-size: 28rpx;\n  color: #333333;\n  font-weight: 500;\n}\n\n.contact-phone {\n  font-size: 28rpx;\n  color: #667eea;\n  font-weight: 500;\n}\n\n/* 浏览统计 */\n.stats {\n  background: #ffffff;\n  padding: 20rpx 30rpx;\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 20rpx;\n}\n\n.stats-text {\n  font-size: 24rpx;\n  color: #999999;\n}\n\n/* 底部操作栏 */\n.bottom-actions {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: #ffffff;\n  display: flex;\n  padding: 20rpx 30rpx;\n  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);\n}\n\n.action-btn {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 20rpx 0;\n  margin: 0 10rpx;\n  border-radius: 12rpx;\n}\n\n.favorite-btn {\n  background: #f8f9fa;\n}\n\n.contact-btn {\n  background: #667eea;\n}\n\n.appointment-btn {\n  background: #ff6b6b;\n}\n\n.action-icon {\n  font-size: 32rpx;\n  margin-bottom: 8rpx;\n}\n\n.action-text {\n  font-size: 24rpx;\n  color: #333333;\n}\n\n.contact-btn .action-text,\n.appointment-btn .action-text {\n  color: #ffffff;\n}\n\n/* 加载和错误状态 */\n.loading, .error {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100vh;\n  background: #f5f5f5;\n}\n\n.loading-text, .error-text {\n  font-size: 28rpx;\n  color: #666666;\n  margin-bottom: 40rpx;\n}\n\n.back-btn {\n  background: #667eea;\n  color: #ffffff;\n  border: none;\n  border-radius: 50rpx;\n  padding: 20rpx 60rpx;\n  font-size: 28rpx;\n}\n</style>", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=3e159eb4&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=3e159eb4&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753838857743\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}