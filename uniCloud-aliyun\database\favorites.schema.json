{"bsonType": "object", "description": "用户收藏表", "required": ["userId", "houseId"], "properties": {"_id": {"description": "收藏记录唯一标识"}, "userId": {"bsonType": "string", "description": "用户ID", "title": "用户ID", "foreignKey": "users._id"}, "houseId": {"bsonType": "string", "description": "房源ID", "title": "房源ID", "foreignKey": "houses._id"}, "createdAt": {"bsonType": "timestamp", "description": "收藏时间", "title": "收藏时间", "forceDefaultValue": {"$env": "now"}}}, "indexes": [{"IndexName": "userId_houseId", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "userId", "Direction": "1"}, {"Name": "houseId", "Direction": "1"}], "MgoIsUnique": true}}, {"IndexName": "userId_createdAt", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "userId", "Direction": "1"}, {"Name": "createdAt", "Direction": "-1"}]}}]}