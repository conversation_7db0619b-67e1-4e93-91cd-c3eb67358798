<block wx:if="{{houseDetail}}"><view class="container data-v-3e159eb4"><swiper class="image-swiper data-v-3e159eb4" indicator-dots="{{true}}" circular="{{true}}"><block wx:for="{{houseDetail.images}}" wx:for-item="image" wx:for-index="index" wx:key="index"><swiper-item class="data-v-3e159eb4"><image class="house-image data-v-3e159eb4" src="{{image}}" mode="aspectFill"></image></swiper-item></block></swiper><view class="house-info data-v-3e159eb4"><view class="house-title data-v-3e159eb4">{{houseDetail.title}}</view><view class="price-info data-v-3e159eb4"><text class="price data-v-3e159eb4">{{"¥"+houseDetail.price}}</text><text class="price-unit data-v-3e159eb4">/月</text></view><view class="house-details data-v-3e159eb4"><block wx:if="{{houseDetail.area}}"><view class="detail-item data-v-3e159eb4"><text class="detail-label data-v-3e159eb4">面积：</text><text class="detail-value data-v-3e159eb4">{{houseDetail.area+"㎡"}}</text></view></block><block wx:if="{{houseDetail.floor}}"><view class="detail-item data-v-3e159eb4"><text class="detail-label data-v-3e159eb4">楼层：</text><text class="detail-value data-v-3e159eb4">{{houseDetail.floor}}</text></view></block><block wx:if="{{houseDetail.orientation}}"><view class="detail-item data-v-3e159eb4"><text class="detail-label data-v-3e159eb4">朝向：</text><text class="detail-value data-v-3e159eb4">{{houseDetail.orientation}}</text></view></block></view><block wx:if="{{$root.g0}}"><view class="house-tags data-v-3e159eb4"><block wx:for="{{houseDetail.tags}}" wx:for-item="tag" wx:for-index="index" wx:key="index"><text class="tag data-v-3e159eb4">{{''+tag+''}}</text></block></view></block></view><view class="section data-v-3e159eb4"><view class="section-title data-v-3e159eb4">房源描述</view><text class="description data-v-3e159eb4">{{houseDetail.description||'暂无描述'}}</text></view><block wx:if="{{$root.g1}}"><view class="section data-v-3e159eb4"><view class="section-title data-v-3e159eb4">配套设施</view><view class="facilities data-v-3e159eb4"><block wx:for="{{houseDetail.facilities}}" wx:for-item="facility" wx:for-index="index" wx:key="index"><view class="facility-item data-v-3e159eb4"><text class="facility-text data-v-3e159eb4">{{facility}}</text></view></block></view></view></block><view class="section data-v-3e159eb4"><view class="section-title data-v-3e159eb4">位置信息</view><text class="address data-v-3e159eb4">{{houseDetail.address}}</text></view><block wx:if="{{houseDetail.contact}}"><view class="section data-v-3e159eb4"><view class="section-title data-v-3e159eb4">联系方式</view><view class="contact-info data-v-3e159eb4"><text class="contact-name data-v-3e159eb4">{{houseDetail.contact.name||'房东'}}</text><text class="contact-phone data-v-3e159eb4">{{houseDetail.contact.phone}}</text></view></view></block><view class="stats data-v-3e159eb4"><text class="stats-text data-v-3e159eb4">{{(houseDetail.viewCount||0)+"人浏览过"}}</text><text class="stats-text data-v-3e159eb4">{{"发布于"+$root.m0}}</text></view><view class="bottom-actions data-v-3e159eb4"><view data-event-opts="{{[['tap',[['toggleFavorite',['$event']]]]]}}" class="action-btn favorite-btn data-v-3e159eb4" bindtap="__e"><text class="action-icon data-v-3e159eb4">{{houseDetail.isFavorited?'❤️':'🤍'}}</text><text class="action-text data-v-3e159eb4">{{houseDetail.isFavorited?'已收藏':'收藏'}}</text></view><view data-event-opts="{{[['tap',[['contactOwner',['$event']]]]]}}" class="action-btn contact-btn data-v-3e159eb4" bindtap="__e"><text class="action-icon data-v-3e159eb4">📞</text><text class="action-text data-v-3e159eb4">联系房东</text></view><view data-event-opts="{{[['tap',[['makeAppointment',['$event']]]]]}}" class="action-btn appointment-btn data-v-3e159eb4" bindtap="__e"><text class="action-icon data-v-3e159eb4">📅</text><text class="action-text data-v-3e159eb4">预约看房</text></view></view></view></block><block wx:else><block wx:if="{{loading}}"><view class="loading data-v-3e159eb4"><text class="loading-text data-v-3e159eb4">加载中...</text></view></block><block wx:else><view class="error data-v-3e159eb4"><text class="error-text data-v-3e159eb4">房源不存在或已下架</text><button data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="back-btn data-v-3e159eb4" bindtap="__e">返回</button></view></block></block>