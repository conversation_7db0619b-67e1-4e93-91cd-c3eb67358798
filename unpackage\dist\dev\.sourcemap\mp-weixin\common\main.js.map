{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/租房管理/App.vue?7380", "webpack:///D:/web/project/租房管理/App.vue?b483", "uni-app:///App.vue", "webpack:///D:/web/project/租房管理/App.vue?7300", "webpack:///D:/web/project/租房管理/App.vue?01be"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "config", "productionTip", "App", "mpType", "app", "$mount", "globalData", "token", "userInfo", "is<PERSON>ogin", "onLaunch", "console", "onShow", "onHide", "methods", "checkLoginStatus", "clearLogin<PERSON>tatus", "uni", "checkNeedLogin", "title", "content", "showCancel", "success", "url", "logout", "icon", "setTimeout"], "mappings": ";;;;;;;;;;;;;;AAAA;AAE2D;AAAuE;AAGlI;AACA;AAAgC;AAAA;AALhC;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAK1DC,YAAG,CAACC,MAAM,CAACC,aAAa,GAAG,KAAK;AAChCC,YAAG,CAACC,MAAM,GAAG,KAAK;AAClB,IAAMC,GAAG,GAAG,IAAIL,YAAG,mBACdG,YAAG,EACN;AACF,UAAAE,GAAG,EAACC,MAAM,EAAE,C;;;;;;;;;;;;;ACZZ;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACa;;;AAG/D;AACmK;AACnK,gBAAgB,gLAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAgmB,CAAgB,4nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;eCCpnB;EACAC;IACAC;IACAC;IACAC;EACA;EAEAC;IACAC;IACA;EACA;EAEAC;IACAD;EACA;EAEAE;IACAF;EACA;EAEAG;IACA;IACAC;MACA;QACA;QACA;QAEA;UACA;UACA;UACA;UACAJ;QACA;UACA;QACA;MACA;QACAA;QACA;MACA;IACA;IAEA;IACAK;MACA;MACA;MACA;MAEA;QACAC;QACAA;MACA;QACAN;MACA;IACA;IAEA;IACAO;MACA;QACAD;UACAE;UACAC;UACAC;UACAC;YACAL;cACAM;YACA;UACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACAP;QACAE;QACAM;MACA;;MAEA;MACAC;QACAT;UACAM;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1FA;AAAA;AAAA;AAAA;AAAm3B,CAAgB,u3BAAG,EAAC,C;;;;;;;;;;;ACAv4B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';import App from './App'\n\n\nimport Vue from 'vue'\nimport './uni.promisify.adaptor'\nVue.config.productionTip = false\nApp.mpType = 'app'\nconst app = new Vue({\n  ...App\n})\napp.$mount()", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\r\n\texport default {\r\n\t\tglobalData: {\r\n\t\t\ttoken: null,\r\n\t\t\tuserInfo: null,\r\n\t\t\tisLogin: false\r\n\t\t},\r\n\r\n\t\tonLaunch: function() {\r\n\t\t\tconsole.log('App Launch')\r\n\t\t\tthis.checkLoginStatus()\r\n\t\t},\r\n\r\n\t\tonShow: function() {\r\n\t\t\tconsole.log('App Show')\r\n\t\t},\r\n\r\n\t\tonHide: function() {\r\n\t\t\tconsole.log('App Hide')\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\t// 检查登录状态\r\n\t\t\tcheckLoginStatus() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst token = uni.getStorageSync('token')\r\n\t\t\t\t\tconst userInfo = uni.getStorageSync('userInfo')\r\n\r\n\t\t\t\t\tif (token && userInfo) {\r\n\t\t\t\t\t\tthis.globalData.token = token\r\n\t\t\t\t\t\tthis.globalData.userInfo = userInfo\r\n\t\t\t\t\t\tthis.globalData.isLogin = true\r\n\t\t\t\t\t\tconsole.log('用户已登录:', userInfo.username)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.clearLoginStatus()\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('检查登录状态失败:', error)\r\n\t\t\t\t\tthis.clearLoginStatus()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 清除登录状态\r\n\t\t\tclearLoginStatus() {\r\n\t\t\t\tthis.globalData.token = null\r\n\t\t\t\tthis.globalData.userInfo = null\r\n\t\t\t\tthis.globalData.isLogin = false\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tuni.removeStorageSync('token')\r\n\t\t\t\t\tuni.removeStorageSync('userInfo')\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('清除本地存储失败:', error)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 检查是否需要登录\r\n\t\t\tcheckNeedLogin() {\r\n\t\t\t\tif (!this.globalData.isLogin) {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\tcontent: '请先登录',\r\n\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\t\t\t\treturn true\r\n\t\t\t},\r\n\r\n\t\t\t// 退出登录\r\n\t\t\tlogout() {\r\n\t\t\t\tthis.clearLoginStatus()\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '已退出登录',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t})\r\n\r\n\t\t\t\t// 跳转到首页\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t\t})\r\n\t\t\t\t}, 1500)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/*每个页面公共css */\r\n</style>\r\n", "import mod from \"-!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753838857708\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}