<template>
	<view class="container">
		<view class="form-container">
			<view class="form-title">发布房源信息</view>
			
			<!-- 房源标题 -->
			<view class="form-item">
				<text class="label">房源标题 *</text>
				<input class="input" type="text" placeholder="请输入房源标题" v-model="formData.title" />
			</view>
			
			<!-- 房源描述 -->
			<view class="form-item">
				<text class="label">房源描述 *</text>
				<textarea class="textarea" placeholder="请详细描述房源情况" v-model="formData.description" />
			</view>
			
			<!-- 租金价格 -->
			<view class="form-item">
				<text class="label">租金价格 *</text>
				<view class="price-input">
					<input class="input" type="number" placeholder="请输入月租金" v-model="formData.price" />
					<text class="price-unit">元/月</text>
				</view>
			</view>
			
			<!-- 房屋面积 -->
			<view class="form-item">
				<text class="label">房屋面积 *</text>
				<view class="area-input">
					<input class="input" type="number" placeholder="请输入面积" v-model="formData.area" />
					<text class="area-unit">平方米</text>
				</view>
			</view>
			
			<!-- 房屋类型 -->
			<view class="form-item">
				<text class="label">房屋类型 *</text>
				<picker :value="typeIndex" :range="typeOptions" @change="onTypeChange">
					<view class="picker">
						{{ typeOptions[typeIndex] || '请选择房屋类型' }}
					</view>
				</picker>
			</view>
			
			<!-- 房屋地址 -->
			<view class="form-item">
				<text class="label">房屋地址 *</text>
				<input class="input" type="text" placeholder="请输入详细地址" v-model="formData.address" />
			</view>
			
			<!-- 联系方式 -->
			<view class="form-item">
				<text class="label">联系电话 *</text>
				<input class="input" type="text" placeholder="请输入联系电话" v-model="formData.contact" />
			</view>
			
			<!-- 房屋标签 -->
			<view class="form-item">
				<text class="label">房屋标签</text>
				<view class="tags-container">
					<view 
						v-for="(tag, index) in availableTags" 
						:key="index"
						class="tag-item"
						:class="{ active: formData.tags.includes(tag) }"
						@click="toggleTag(tag)"
					>
						{{ tag }}
					</view>
				</view>
			</view>
			
			<!-- 房屋图片 -->
			<view class="form-item">
				<text class="label">房屋图片</text>
				<view class="image-upload">
					<view 
						v-for="(image, index) in formData.images" 
						:key="index"
						class="image-item"
					>
						<image :src="image" mode="aspectFill" class="uploaded-image" />
						<view class="delete-btn" @click="removeImage(index)">×</view>
					</view>
					<view class="upload-btn" @click="chooseImage" v-if="formData.images.length < 6">
						<text class="upload-text">+</text>
						<text class="upload-tip">添加图片</text>
					</view>
				</view>
				<text class="tip">最多上传6张图片</text>
			</view>
			
			<!-- 房屋设施 -->
			<view class="form-item">
				<text class="label">房屋设施</text>
				<view class="facilities-container">
					<view 
						v-for="(facility, index) in availableFacilities" 
						:key="index"
						class="facility-item"
						:class="{ active: formData.facilities.includes(facility) }"
						@click="toggleFacility(facility)"
					>
						{{ facility }}
					</view>
				</view>
			</view>
			
			<!-- 提交按钮 -->
			<view class="submit-container">
				<button class="submit-btn" @click="submitForm" :disabled="submitting">
					{{ submitting ? '发布中...' : '发布房源' }}
				</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			submitting: false,
			typeIndex: 0,
			typeOptions: ['一室一厅', '两室一厅', '三室一厅', '三室两厅', '四室两厅', '单间', '合租'],
			availableTags: ['精装修', '家具齐全', '交通便利', '近地铁', '拎包入住', '独立卫浴', '阳台', '停车位'],
			availableFacilities: ['空调', '洗衣机', '冰箱', '热水器', 'WiFi', '电视', '微波炉', '衣柜', '书桌', '床'],
			formData: {
				title: '',
				description: '',
				price: '',
				area: '',
				type: '',
				address: '',
				contact: '',
				tags: [],
				images: [],
				facilities: []
			}
		}
	},
	methods: {
		onTypeChange(e) {
			this.typeIndex = e.detail.value
			this.formData.type = this.typeOptions[e.detail.value]
		},
		
		toggleTag(tag) {
			const index = this.formData.tags.indexOf(tag)
			if (index > -1) {
				this.formData.tags.splice(index, 1)
			} else {
				this.formData.tags.push(tag)
			}
		},
		
		toggleFacility(facility) {
			const index = this.formData.facilities.indexOf(facility)
			if (index > -1) {
				this.formData.facilities.splice(index, 1)
			} else {
				this.formData.facilities.push(facility)
			}
		},
		
		chooseImage() {
			uni.chooseImage({
				count: 6 - this.formData.images.length,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					this.formData.images.push(...res.tempFilePaths)
				}
			})
		},
		
		removeImage(index) {
			this.formData.images.splice(index, 1)
		},
		
		validateForm() {
			if (!this.formData.title.trim()) {
				uni.showToast({ title: '请输入房源标题', icon: 'none' })
				return false
			}
			if (!this.formData.description.trim()) {
				uni.showToast({ title: '请输入房源描述', icon: 'none' })
				return false
			}
			if (!this.formData.price || this.formData.price <= 0) {
				uni.showToast({ title: '请输入正确的租金价格', icon: 'none' })
				return false
			}
			if (!this.formData.area || this.formData.area <= 0) {
				uni.showToast({ title: '请输入正确的房屋面积', icon: 'none' })
				return false
			}
			if (!this.formData.type) {
				uni.showToast({ title: '请选择房屋类型', icon: 'none' })
				return false
			}
			if (!this.formData.address.trim()) {
				uni.showToast({ title: '请输入房屋地址', icon: 'none' })
				return false
			}
			if (!this.formData.contact.trim()) {
				uni.showToast({ title: '请输入联系电话', icon: 'none' })
				return false
			}
			return true
		},
		
		async submitForm() {
			if (!this.validateForm()) return
			
			this.submitting = true
			
			try {
				// 上传图片
				const imageUrls = []
				for (let imagePath of this.formData.images) {
					const uploadResult = await uniCloud.uploadFile({
						filePath: imagePath,
						cloudPath: `houses/${Date.now()}_${Math.random().toString(36).substr(2, 9)}.jpg`
					})
					imageUrls.push(uploadResult.fileID)
				}
				
				// 调用云函数发布房源
				const result = await uniCloud.callFunction({
					name: 'publishHouse',
					data: {
						...this.formData,
						images: imageUrls,
						price: Number(this.formData.price),
						area: Number(this.formData.area)
					}
				})
				
				if (result.result.success) {
					uni.showToast({ title: '发布成功', icon: 'success' })
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
				} else {
					uni.showToast({ title: result.result.message || '发布失败', icon: 'none' })
				}
			} catch (error) {
				console.error('发布房源失败:', error)
				uni.showToast({ title: '发布失败，请重试', icon: 'none' })
			} finally {
				this.submitting = false
			}
		}
	}
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 20rpx;
}

.form-container {
	background: white;
	border-radius: 20rpx;
	padding: 40rpx;
	margin: 20rpx 0;
}

.form-title {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
	text-align: center;
	margin-bottom: 60rpx;
}

.form-item {
	margin-bottom: 40rpx;
}

.label {
	display: block;
	font-size: 32rpx;
	color: #333;
	margin-bottom: 20rpx;
	font-weight: 500;
}

.input, .textarea {
	width: 100%;
	padding: 24rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	font-size: 30rpx;
	background: #fafafa;
}

.textarea {
	height: 200rpx;
	resize: none;
}

.price-input, .area-input {
	display: flex;
	align-items: center;
}

.price-input .input, .area-input .input {
	flex: 1;
	margin-right: 20rpx;
}

.price-unit, .area-unit {
	font-size: 28rpx;
	color: #666;
}

.picker {
	padding: 24rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	background: #fafafa;
	font-size: 30rpx;
	color: #333;
}

.tags-container, .facilities-container {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}

.tag-item, .facility-item {
	padding: 16rpx 32rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 40rpx;
	font-size: 28rpx;
	color: #666;
	background: #fafafa;
}

.tag-item.active, .facility-item.active {
	background: #667eea;
	color: white;
	border-color: #667eea;
}

.image-upload {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}

.image-item {
	position: relative;
	width: 200rpx;
	height: 200rpx;
}

.uploaded-image {
	width: 100%;
	height: 100%;
	border-radius: 12rpx;
}

.delete-btn {
	position: absolute;
	top: -10rpx;
	right: -10rpx;
	width: 40rpx;
	height: 40rpx;
	background: #ff4757;
	color: white;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
}

.upload-btn {
	width: 200rpx;
	height: 200rpx;
	border: 2rpx dashed #ccc;
	border-radius: 12rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background: #fafafa;
}

.upload-text {
	font-size: 60rpx;
	color: #ccc;
}

.upload-tip {
	font-size: 24rpx;
	color: #999;
	margin-top: 10rpx;
}

.tip {
	font-size: 24rpx;
	color: #999;
	margin-top: 10rpx;
}

.submit-container {
	margin-top: 60rpx;
}

.submit-btn {
	width: 100%;
	height: 88rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 44rpx;
	font-size: 32rpx;
	font-weight: bold;
}

.submit-btn:disabled {
	opacity: 0.6;
}
</style>
