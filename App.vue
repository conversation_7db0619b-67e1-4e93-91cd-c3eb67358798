<script>
	export default {
		globalData: {
			token: null,
			userInfo: null,
			isLogin: false
		},

		onLaunch: function() {
			console.log('App Launch')
			this.checkLoginStatus()
		},

		onShow: function() {
			console.log('App Show')
		},

		onHide: function() {
			console.log('App Hide')
		},

		methods: {
			// 检查登录状态
			checkLoginStatus() {
				try {
					const token = uni.getStorageSync('token')
					const userInfo = uni.getStorageSync('userInfo')

					if (token && userInfo) {
						this.globalData.token = token
						this.globalData.userInfo = userInfo
						this.globalData.isLogin = true
						console.log('用户已登录:', userInfo.username)
					} else {
						this.clearLoginStatus()
					}
				} catch (error) {
					console.error('检查登录状态失败:', error)
					this.clearLoginStatus()
				}
			},

			// 清除登录状态
			clearLoginStatus() {
				this.globalData.token = null
				this.globalData.userInfo = null
				this.globalData.isLogin = false

				try {
					uni.removeStorageSync('token')
					uni.removeStorageSync('userInfo')
				} catch (error) {
					console.error('清除本地存储失败:', error)
				}
			},

			// 检查是否需要登录
			checkNeedLogin() {
				if (!this.globalData.isLogin) {
					uni.showModal({
						title: '提示',
						content: '请先登录',
						showCancel: false,
						success: () => {
							uni.navigateTo({
								url: '/pages/login/login'
							})
						}
					})
					return false
				}
				return true
			},

			// 退出登录
			logout() {
				this.clearLoginStatus()
				uni.showToast({
					title: '已退出登录',
					icon: 'success'
				})

				// 跳转到首页
				setTimeout(() => {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				}, 1500)
			}
		}
	}
</script>

<style>
	/*每个页面公共css */
</style>
