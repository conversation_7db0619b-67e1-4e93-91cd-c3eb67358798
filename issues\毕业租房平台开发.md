# 毕业租房平台开发任务

## 项目概述
基于 uni-app + uniCloud 的毕业租房管理平台，包含用户前端小程序和管理后台。

## 技术栈
- 前端：Vue2 + uni-app
- 后端：uniCloud 云函数 + MongoDB
- 管理后台：uni-admin

## 开发计划（渐进式开发）

### 阶段1：数据库设计 + 基础云函数框架 ✅ 已完成
- [x] 创建数据库Schema文件（users, houses, favorites, appointments）
- [x] 创建云函数公共模块（response-util, auth-util, db-util）
- [x] 配置uni-id用户体系

### 阶段2：用户登录功能 ✅ 已完成
- [x] 创建登录/注册云函数（loginUser, registerUser）
- [x] 开发登录页面（login.vue, register.vue）
- [x] 全局登录状态管理（App.vue更新）

### 阶段3：房源列表功能
- [ ] 创建房源相关云函数
- [ ] 重构首页为房源列表
- [ ] 创建房源详情页

### 阶段4：房源详情与收藏功能
- [ ] 创建收藏相关云函数
- [ ] 增强房源详情页
- [ ] 创建我的收藏页面

### 阶段5：预约看房功能
- [ ] 创建预约相关云函数
- [ ] 增强房源详情页预约功能
- [ ] 创建我的预约页面

### 阶段6：管理后台集成
- [ ] 创建数据库Schema配置
- [ ] 生成管理页面
- [ ] 配置管理后台菜单

## 数据库设计
### users 集合
- _id: 用户唯一标识
- username: 用户名
- password: 加密密码
- createdAt: 创建时间

### houses 集合
- _id: 房源唯一标识
- title: 房源标题
- description: 详细描述
- images: 图片URL数组
- price: 租金价格
- address: 房源地址
- tags: 标签数组
- status: 状态（available/unavailable）
- createdAt: 创建时间

### favorites 集合
- _id: 收藏记录标识
- userId: 用户ID
- houseId: 房源ID
- createdAt: 收藏时间

### appointments 集合
- _id: 预约记录标识
- userId: 用户ID
- houseId: 房源ID
- appointmentTime: 预约时间
- status: 预约状态（pending/confirmed/cancelled）
- createdAt: 创建时间
