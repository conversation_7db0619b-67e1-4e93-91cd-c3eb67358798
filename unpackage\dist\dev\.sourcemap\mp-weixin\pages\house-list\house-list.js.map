{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/租房管理/pages/house-list/house-list.vue?7228", "webpack:///D:/web/project/租房管理/pages/house-list/house-list.vue?0410", "webpack:///D:/web/project/租房管理/pages/house-list/house-list.vue?c35f", "webpack:///D:/web/project/租房管理/pages/house-list/house-list.vue?a787", "uni-app:///pages/house-list/house-list.vue", "webpack:///D:/web/project/租房管理/pages/house-list/house-list.vue?95b8", "webpack:///D:/web/project/租房管理/pages/house-list/house-list.vue?b95f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "HouseCard", "data", "houseList", "searchKeyword", "currentPage", "pageSize", "loading", "refreshing", "noMore", "sortBy", "sortOrder", "minPrice", "maxPrice", "computed", "sortText", "priceText", "onLoad", "methods", "loadHouseList", "isRefresh", "uniCloud", "name", "page", "keyword", "result", "uni", "title", "icon", "console", "handleSearch", "onRefresh", "loadMore", "showSortModal", "itemList", "success", "showPriceModal"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACqC;;;AAG9F;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mMAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5CA;AAAA;AAAA;AAAA;AAAqoB,CAAgB,moBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;eCkEzpB;EACAC;IACAC;EACA;EAEAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACAC;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;MACA;MACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAEA;kBACA;kBACA;gBACA;gBAAA;gBAAA;gBAAA,OAGAC;kBACAC;kBACApB;oBACAqB;oBACAjB;oBACAkB;oBACAZ;oBACAC;oBACAH;oBACAC;kBACA;gBACA;cAAA;gBAXAc;gBAaA;kBAAA,sBACAA;kBAEA;oBACA;kBACA;oBACA;kBACA;kBAEA;kBAEA;oBACA;kBACA;gBACA;kBACAC;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;gBACAH;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAE;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MAEAP;QACAQ;QACAC;UACA;UACA;YACA;cACA;cACA;cACA;YACA;cACA;cACA;cACA;YACA;cACA;cACA;cACA;YACA;cACA;cACA;cACA;UAAA;UAEA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MAEAV;QACAQ;QACAC;UACA;UACA;YACA;cACA;cACA;cACA;YACA;cACA;cACA;cACA;YACA;cACA;cACA;cACA;YACA;cACA;cACA;cACA;YACA;cACA;cACA;cACA;YACA;cACA;cACA;cACA;UAAA;UAEA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzQA;AAAA;AAAA;AAAA;AAA47B,CAAgB,s5BAAG,EAAC,C;;;;;;;;;;;ACAh9B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/house-list/house-list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/house-list/house-list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./house-list.vue?vue&type=template&id=290fc666&scoped=true&\"\nvar renderjs\nimport script from \"./house-list.vue?vue&type=script&lang=js&\"\nexport * from \"./house-list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./house-list.vue?vue&type=style&index=0&id=290fc666&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"290fc666\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/house-list/house-list.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./house-list.vue?vue&type=template&id=290fc666&scoped=true&\"", "var components\ntry {\n  components = {\n    houseCard: function () {\n      return import(\n        /* webpackChunkName: \"components/house-card/house-card\" */ \"@/components/house-card/house-card.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.houseList.length\n  var g1 = !_vm.loading && _vm.houseList.length === 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./house-list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./house-list.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <!-- 搜索栏 -->\n    <view class=\"search-bar\">\n      <view class=\"search-input-wrapper\">\n        <input\n          class=\"search-input\"\n          type=\"text\"\n          placeholder=\"搜索房源、地址...\"\n          v-model=\"searchKeyword\"\n          @confirm=\"handleSearch\"\n        />\n        <view class=\"search-btn\" @click=\"handleSearch\">\n          <text class=\"search-icon\">🔍</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 筛选栏 -->\n    <view class=\"filter-bar\">\n      <view class=\"filter-item\" @click=\"showSortModal\">\n        <text class=\"filter-text\">{{ sortText }}</text>\n        <text class=\"filter-arrow\">▼</text>\n      </view>\n\n      <view class=\"filter-item\" @click=\"showPriceModal\">\n        <text class=\"filter-text\">{{ priceText }}</text>\n        <text class=\"filter-arrow\">▼</text>\n      </view>\n    </view>\n\n    <!-- 房源列表 -->\n    <scroll-view\n      class=\"house-list\"\n      scroll-y\n      @scrolltolower=\"loadMore\"\n      :refresher-enabled=\"true\"\n      :refresher-triggered=\"refreshing\"\n      @refresherrefresh=\"onRefresh\"\n    >\n      <view class=\"list-content\">\n        <house-card\n          v-for=\"house in houseList\"\n          :key=\"house._id\"\n          :house=\"house\"\n        />\n\n        <!-- 加载状态 -->\n        <view class=\"load-status\" v-if=\"houseList.length > 0\">\n          <text class=\"status-text\" v-if=\"loading\">加载中...</text>\n          <text class=\"status-text\" v-else-if=\"noMore\">没有更多了</text>\n        </view>\n\n        <!-- 空状态 -->\n        <view class=\"empty-state\" v-if=\"!loading && houseList.length === 0\">\n          <text class=\"empty-text\">暂无房源信息</text>\n          <text class=\"empty-desc\">请稍后再试或联系管理员</text>\n        </view>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nimport HouseCard from '@/components/house-card/house-card.vue'\n\nexport default {\n  components: {\n    HouseCard\n  },\n\n  data() {\n    return {\n      houseList: [],\n      searchKeyword: '',\n      currentPage: 1,\n      pageSize: 10,\n      loading: false,\n      refreshing: false,\n      noMore: false,\n\n      // 筛选条件\n      sortBy: 'createdAt',\n      sortOrder: 'desc',\n      minPrice: 0,\n      maxPrice: 0\n    }\n  },\n\n  computed: {\n    sortText() {\n      if (this.sortBy === 'price') {\n        return this.sortOrder === 'asc' ? '价格升序' : '价格降序'\n      } else if (this.sortBy === 'viewCount') {\n        return '热度排序'\n      } else {\n        return '最新发布'\n      }\n    },\n\n    priceText() {\n      if (this.minPrice > 0 || this.maxPrice > 0) {\n        if (this.minPrice > 0 && this.maxPrice > 0) {\n          return `¥${this.minPrice}-${this.maxPrice}`\n        } else if (this.minPrice > 0) {\n          return `¥${this.minPrice}以上`\n        } else {\n          return `¥${this.maxPrice}以下`\n        }\n      }\n      return '价格'\n    }\n  },\n\n  onLoad() {\n    this.loadHouseList()\n  },\n\n  methods: {\n    // 加载房源列表\n    async loadHouseList(isRefresh = false) {\n      if (this.loading) return\n\n      this.loading = true\n\n      if (isRefresh) {\n        this.currentPage = 1\n        this.noMore = false\n      }\n\n      try {\n        const result = await uniCloud.callFunction({\n          name: 'listHouses',\n          data: {\n            page: this.currentPage,\n            pageSize: this.pageSize,\n            keyword: this.searchKeyword,\n            minPrice: this.minPrice,\n            maxPrice: this.maxPrice,\n            sortBy: this.sortBy,\n            sortOrder: this.sortOrder\n          }\n        })\n\n        if (result.result.code === 0) {\n          const { data, totalPages } = result.result.data\n\n          if (isRefresh) {\n            this.houseList = data\n          } else {\n            this.houseList = [...this.houseList, ...data]\n          }\n\n          this.noMore = this.currentPage >= totalPages\n\n          if (!this.noMore) {\n            this.currentPage++\n          }\n        } else {\n          uni.showToast({\n            title: result.result.message || '加载失败',\n            icon: 'none'\n          })\n        }\n\n      } catch (error) {\n        console.error('加载房源列表失败:', error)\n        uni.showToast({\n          title: '网络错误，请重试',\n          icon: 'none'\n        })\n      } finally {\n        this.loading = false\n        this.refreshing = false\n      }\n    },\n\n    // 搜索\n    handleSearch() {\n      this.loadHouseList(true)\n    },\n\n    // 下拉刷新\n    onRefresh() {\n      this.refreshing = true\n      this.loadHouseList(true)\n    },\n\n    // 加载更多\n    loadMore() {\n      if (!this.noMore && !this.loading) {\n        this.loadHouseList()\n      }\n    },\n\n    // 显示排序选择\n    showSortModal() {\n      const items = ['最新发布', '价格升序', '价格降序', '热度排序']\n\n      uni.showActionSheet({\n        itemList: items,\n        success: (res) => {\n          const index = res.tapIndex\n          switch (index) {\n            case 0:\n              this.sortBy = 'createdAt'\n              this.sortOrder = 'desc'\n              break\n            case 1:\n              this.sortBy = 'price'\n              this.sortOrder = 'asc'\n              break\n            case 2:\n              this.sortBy = 'price'\n              this.sortOrder = 'desc'\n              break\n            case 3:\n              this.sortBy = 'viewCount'\n              this.sortOrder = 'desc'\n              break\n          }\n          this.loadHouseList(true)\n        }\n      })\n    },\n\n    // 显示价格筛选\n    showPriceModal() {\n      const items = ['不限', '1000以下', '1000-2000', '2000-3000', '3000-5000', '5000以上']\n\n      uni.showActionSheet({\n        itemList: items,\n        success: (res) => {\n          const index = res.tapIndex\n          switch (index) {\n            case 0:\n              this.minPrice = 0\n              this.maxPrice = 0\n              break\n            case 1:\n              this.minPrice = 0\n              this.maxPrice = 1000\n              break\n            case 2:\n              this.minPrice = 1000\n              this.maxPrice = 2000\n              break\n            case 3:\n              this.minPrice = 2000\n              this.maxPrice = 3000\n              break\n            case 4:\n              this.minPrice = 3000\n              this.maxPrice = 5000\n              break\n            case 5:\n              this.minPrice = 5000\n              this.maxPrice = 0\n              break\n          }\n          this.loadHouseList(true)\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.container {\n  background: #f5f5f5;\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n}\n\n/* 搜索栏 */\n.search-bar {\n  background: #ffffff;\n  padding: 20rpx 30rpx;\n  border-bottom: 1rpx solid #eeeeee;\n}\n\n.search-input-wrapper {\n  display: flex;\n  align-items: center;\n  background: #f5f5f5;\n  border-radius: 50rpx;\n  padding: 0 30rpx;\n}\n\n.search-input {\n  flex: 1;\n  height: 80rpx;\n  font-size: 28rpx;\n  color: #333333;\n}\n\n.search-btn {\n  padding: 10rpx;\n}\n\n.search-icon {\n  font-size: 32rpx;\n  color: #999999;\n}\n\n/* 筛选栏 */\n.filter-bar {\n  background: #ffffff;\n  display: flex;\n  align-items: center;\n  padding: 20rpx 30rpx;\n  border-bottom: 1rpx solid #eeeeee;\n}\n\n.filter-item {\n  display: flex;\n  align-items: center;\n  margin-right: 40rpx;\n  padding: 10rpx 20rpx;\n  background: #f8f9fa;\n  border-radius: 20rpx;\n}\n\n.filter-text {\n  font-size: 26rpx;\n  color: #333333;\n  margin-right: 8rpx;\n}\n\n.filter-arrow {\n  font-size: 20rpx;\n  color: #999999;\n}\n\n/* 房源列表 */\n.house-list {\n  flex: 1;\n  padding: 20rpx 30rpx;\n}\n\n.list-content {\n  padding-bottom: 40rpx;\n}\n\n/* 加载状态 */\n.load-status {\n  text-align: center;\n  padding: 40rpx 0;\n}\n\n.status-text {\n  font-size: 26rpx;\n  color: #999999;\n}\n\n/* 空状态 */\n.empty-state {\n  text-align: center;\n  padding: 120rpx 0;\n}\n\n.empty-text {\n  font-size: 32rpx;\n  color: #666666;\n  margin-bottom: 20rpx;\n  display: block;\n}\n\n.empty-desc {\n  font-size: 26rpx;\n  color: #999999;\n  display: block;\n}\n</style>", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./house-list.vue?vue&type=style&index=0&id=290fc666&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./house-list.vue?vue&type=style&index=0&id=290fc666&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753838857727\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}