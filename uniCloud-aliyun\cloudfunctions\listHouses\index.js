'use strict';

const { asyncWrapper, success, paramError } = require('response-util')
const { paginate, getCollection } = require('db-util')

exports.main = asyncWrapper(async (event, context) => {
  const { 
    page = 1, 
    pageSize = 10, 
    keyword = '', 
    minPrice = 0, 
    maxPrice = 0,
    tags = [],
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = event
  
  // 参数验证
  if (page < 1) {
    return paramError('页码不能小于1')
  }
  
  if (pageSize < 1 || pageSize > 50) {
    return paramError('每页数量应在1-50之间')
  }
  
  // 构建查询条件
  const where = {
    status: 'available' // 只查询可用房源
  }
  
  // 关键词搜索（标题或地址）
  if (keyword.trim()) {
    const keywordRegex = new RegExp(keyword.trim(), 'i')
    where.$or = [
      { title: keywordRegex },
      { address: keywordRegex },
      { description: keywordRegex }
    ]
  }
  
  // 价格筛选
  if (minPrice > 0 || maxPrice > 0) {
    where.price = {}
    if (minPrice > 0) {
      where.price.$gte = minPrice
    }
    if (maxPrice > 0) {
      where.price.$lte = maxPrice
    }
  }
  
  // 标签筛选
  if (Array.isArray(tags) && tags.length > 0) {
    where.tags = {
      $in: tags
    }
  }
  
  // 构建排序条件
  const orderBy = {}
  if (sortBy === 'price') {
    orderBy.price = sortOrder === 'asc' ? 'asc' : 'desc'
  } else if (sortBy === 'viewCount') {
    orderBy.viewCount = sortOrder === 'asc' ? 'asc' : 'desc'
  } else {
    orderBy.createdAt = sortOrder === 'asc' ? 'asc' : 'desc'
  }
  
  // 字段筛选（列表页不需要完整描述）
  const field = {
    _id: true,
    title: true,
    price: true,
    address: true,
    tags: true,
    images: true,
    area: true,
    floor: true,
    orientation: true,
    viewCount: true,
    createdAt: true
  }
  
  try {
    const housesCollection = getCollection('houses')
    const result = await paginate(housesCollection, {
      page,
      pageSize,
      where,
      orderBy,
      field
    })
    
    // 处理图片数组，只返回第一张作为缩略图
    result.data = result.data.map(house => ({
      ...house,
      thumbnail: house.images && house.images.length > 0 ? house.images[0] : null,
      imageCount: house.images ? house.images.length : 0
    }))
    
    return success(result, '获取房源列表成功')
    
  } catch (error) {
    console.error('获取房源列表失败:', error)
    throw error
  }
})
