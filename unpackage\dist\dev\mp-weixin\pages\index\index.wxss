
.container.data-v-57280228 {
  background: #f5f5f5;
  min-height: 100vh;
}

/* 轮播图 */
.banner.data-v-57280228 {
  width: 100%;
  height: 400rpx;
}
.banner-image.data-v-57280228 {
  width: 100%;
  height: 100%;
}

/* 快捷导航 */
.nav-grid.data-v-57280228 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  background: #ffffff;
  padding: 40rpx 0;
  margin-bottom: 20rpx;
}
.nav-item.data-v-57280228 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.nav-icon.data-v-57280228 {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}
.nav-text.data-v-57280228 {
  font-size: 26rpx;
  color: #333333;
}

/* 推荐房源 */
.section.data-v-57280228 {
  background: #ffffff;
  margin-bottom: 20rpx;
  padding: 30rpx;
}
.section-header.data-v-57280228 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.section-title.data-v-57280228 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}
.section-more.data-v-57280228 {
  font-size: 26rpx;
  color: #667eea;
}
.house-grid.data-v-57280228 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}
.house-item.data-v-57280228 {
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.house-image.data-v-57280228 {
  width: 100%;
  height: 240rpx;
}
.house-info.data-v-57280228 {
  padding: 20rpx;
  background: #ffffff;
}
.house-title.data-v-57280228 {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.house-price.data-v-57280228 {
  font-size: 26rpx;
  color: #ff6b6b;
  font-weight: bold;
  margin-bottom: 8rpx;
}
.house-address.data-v-57280228 {
  font-size: 24rpx;
  color: #999999;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 用户操作区 */
.user-section.data-v-57280228 {
  background: #ffffff;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
}
.login-prompt.data-v-57280228 {
  text-align: center;
}
.prompt-text.data-v-57280228 {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 30rpx;
  display: block;
}
.login-btn.data-v-57280228 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
}

