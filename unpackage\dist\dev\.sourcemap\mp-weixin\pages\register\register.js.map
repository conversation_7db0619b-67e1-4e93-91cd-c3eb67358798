{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/租房管理/pages/register/register.vue?7b22", "webpack:///D:/web/project/租房管理/pages/register/register.vue?9024", "webpack:///D:/web/project/租房管理/pages/register/register.vue?4073", "webpack:///D:/web/project/租房管理/pages/register/register.vue?43cf", "uni-app:///pages/register/register.vue", "webpack:///D:/web/project/租房管理/pages/register/register.vue?0946", "webpack:///D:/web/project/租房管理/pages/register/register.vue?897c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "formData", "username", "password", "confirmPassword", "nickname", "phone", "email", "loading", "methods", "validateForm", "uni", "title", "icon", "handleRegister", "Object", "registerData", "uniCloud", "name", "result", "getApp", "setTimeout", "url", "console", "goToLogin"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACqC;;;AAG5F;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmoB,CAAgB,ioBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCqFvpB;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;QAAAR;QAAAC;QAAAC;QAAAE;QAAAC;MAEA;QACAI;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACA;QACAF;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIA;gBAAA;gBAAA,kBAGA,sJAEA;gBACAC;kBACA;oBACA;kBACA;oBACAC;kBACA;gBACA;gBAAA;gBAAA,OAEAC;kBACAC;kBACAlB;gBACA;cAAA;gBAHAmB;gBAKA;kBACA;kBAAA,sBACAA,gGAEA;kBACAR;kBACAA;;kBAEA;kBACAS;kBACAA;kBACAA;kBAEAT;oBACAC;oBACAC;kBACA;;kBAEA;kBACAQ;oBACAV;sBACAW;oBACA;kBACA;gBAEA;kBACAX;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAU;gBACAZ;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAW;MACAb;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/OA;AAAA;AAAA;AAAA;AAA07B,CAAgB,o5BAAG,EAAC,C;;;;;;;;;;;ACA98B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/register/register.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/register/register.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./register.vue?vue&type=template&id=891c2434&scoped=true&\"\nvar renderjs\nimport script from \"./register.vue?vue&type=script&lang=js&\"\nexport * from \"./register.vue?vue&type=script&lang=js&\"\nimport style0 from \"./register.vue?vue&type=style&index=0&id=891c2434&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"891c2434\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/register/register.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=template&id=891c2434&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"register-container\">\n    <view class=\"register-header\">\n      <text class=\"page-title\">注册账号</text>\n      <text class=\"page-desc\">加入毕业租房，开始找房之旅</text>\n    </view>\n    \n    <view class=\"register-form\">\n      <view class=\"form-item\">\n        <input \n          class=\"form-input\" \n          type=\"text\" \n          placeholder=\"请输入用户名（3-20个字符）\"\n          v-model=\"formData.username\"\n          :disabled=\"loading\"\n        />\n      </view>\n      \n      <view class=\"form-item\">\n        <input \n          class=\"form-input\" \n          type=\"password\" \n          placeholder=\"请输入密码（至少6个字符）\"\n          v-model=\"formData.password\"\n          :disabled=\"loading\"\n        />\n      </view>\n      \n      <view class=\"form-item\">\n        <input \n          class=\"form-input\" \n          type=\"password\" \n          placeholder=\"请确认密码\"\n          v-model=\"formData.confirmPassword\"\n          :disabled=\"loading\"\n        />\n      </view>\n      \n      <view class=\"form-item\">\n        <input \n          class=\"form-input\" \n          type=\"text\" \n          placeholder=\"请输入昵称（可选）\"\n          v-model=\"formData.nickname\"\n          :disabled=\"loading\"\n        />\n      </view>\n      \n      <view class=\"form-item\">\n        <input \n          class=\"form-input\" \n          type=\"text\" \n          placeholder=\"请输入手机号（可选）\"\n          v-model=\"formData.phone\"\n          :disabled=\"loading\"\n        />\n      </view>\n      \n      <view class=\"form-item\">\n        <input \n          class=\"form-input\" \n          type=\"text\" \n          placeholder=\"请输入邮箱（可选）\"\n          v-model=\"formData.email\"\n          :disabled=\"loading\"\n        />\n      </view>\n      \n      <button \n        class=\"register-btn\" \n        :class=\"{ 'register-btn-disabled': loading }\"\n        :disabled=\"loading\"\n        @click=\"handleRegister\"\n      >\n        {{ loading ? '注册中...' : '注册' }}\n      </button>\n      \n      <view class=\"form-footer\">\n        <text class=\"login-link\" @click=\"goToLogin\">已有账号？立即登录</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      formData: {\n        username: '',\n        password: '',\n        confirmPassword: '',\n        nickname: '',\n        phone: '',\n        email: ''\n      },\n      loading: false\n    }\n  },\n  \n  methods: {\n    // 表单验证\n    validateForm() {\n      const { username, password, confirmPassword, phone, email } = this.formData\n      \n      if (!username.trim()) {\n        uni.showToast({\n          title: '请输入用户名',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      if (username.length < 3 || username.length > 20) {\n        uni.showToast({\n          title: '用户名长度应在3-20个字符之间',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      if (!password.trim()) {\n        uni.showToast({\n          title: '请输入密码',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      if (password.length < 6) {\n        uni.showToast({\n          title: '密码长度不能少于6个字符',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      if (password !== confirmPassword) {\n        uni.showToast({\n          title: '两次输入的密码不一致',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      // 验证手机号格式（如果填写）\n      if (phone && !/^1[3-9]\\d{9}$/.test(phone)) {\n        uni.showToast({\n          title: '手机号格式不正确',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      // 验证邮箱格式（如果填写）\n      if (email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email)) {\n        uni.showToast({\n          title: '邮箱格式不正确',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      return true\n    },\n    \n    // 处理注册\n    async handleRegister() {\n      if (!this.validateForm()) {\n        return\n      }\n      \n      this.loading = true\n      \n      try {\n        const { confirmPassword, ...registerData } = this.formData\n        \n        // 清理空字段\n        Object.keys(registerData).forEach(key => {\n          if (!registerData[key].trim()) {\n            delete registerData[key]\n          } else {\n            registerData[key] = registerData[key].trim()\n          }\n        })\n        \n        const result = await uniCloud.callFunction({\n          name: 'registerUser',\n          data: registerData\n        })\n        \n        if (result.result.code === 0) {\n          // 注册成功，保存用户信息和token\n          const { token, userInfo } = result.result.data\n          \n          // 保存到本地存储\n          uni.setStorageSync('token', token)\n          uni.setStorageSync('userInfo', userInfo)\n          \n          // 更新全局状态\n          getApp().globalData.token = token\n          getApp().globalData.userInfo = userInfo\n          getApp().globalData.isLogin = true\n          \n          uni.showToast({\n            title: '注册成功',\n            icon: 'success'\n          })\n          \n          // 延迟跳转\n          setTimeout(() => {\n            uni.reLaunch({\n              url: '/pages/index/index'\n            })\n          }, 1500)\n          \n        } else {\n          uni.showToast({\n            title: result.result.message || '注册失败',\n            icon: 'none'\n          })\n        }\n        \n      } catch (error) {\n        console.error('注册错误:', error)\n        uni.showToast({\n          title: '网络错误，请重试',\n          icon: 'none'\n        })\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 跳转到登录页面\n    goToLogin() {\n      uni.navigateBack()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.register-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 0 60rpx;\n  padding-top: 100rpx;\n}\n\n.register-header {\n  text-align: center;\n  margin-bottom: 80rpx;\n}\n\n.page-title {\n  display: block;\n  font-size: 48rpx;\n  font-weight: bold;\n  color: #ffffff;\n  margin-bottom: 20rpx;\n}\n\n.page-desc {\n  display: block;\n  font-size: 28rpx;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.register-form {\n  background: #ffffff;\n  border-radius: 20rpx;\n  padding: 60rpx 40rpx;\n  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);\n}\n\n.form-item {\n  margin-bottom: 30rpx;\n}\n\n.form-input {\n  width: 100%;\n  height: 88rpx;\n  background: #f8f9fa;\n  border: 2rpx solid #e9ecef;\n  border-radius: 12rpx;\n  padding: 0 30rpx;\n  font-size: 32rpx;\n  color: #333333;\n  box-sizing: border-box;\n}\n\n.form-input:focus {\n  border-color: #667eea;\n  background: #ffffff;\n}\n\n.register-btn {\n  width: 100%;\n  height: 88rpx;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: #ffffff;\n  border: none;\n  border-radius: 12rpx;\n  font-size: 32rpx;\n  font-weight: bold;\n  margin-top: 20rpx;\n}\n\n.register-btn-disabled {\n  opacity: 0.6;\n}\n\n.form-footer {\n  text-align: center;\n  margin-top: 40rpx;\n}\n\n.login-link {\n  color: #667eea;\n  font-size: 28rpx;\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=style&index=0&id=891c2434&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=style&index=0&id=891c2434&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753838857738\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}