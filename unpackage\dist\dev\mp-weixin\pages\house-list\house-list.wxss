
.container.data-v-290fc666 {
  background: #f5f5f5;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 搜索栏 */
.search-bar.data-v-290fc666 {
  background: #ffffff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eeeeee;
}
.search-input-wrapper.data-v-290fc666 {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 50rpx;
  padding: 0 30rpx;
}
.search-input.data-v-290fc666 {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333333;
}
.search-btn.data-v-290fc666 {
  padding: 10rpx;
}
.search-icon.data-v-290fc666 {
  font-size: 32rpx;
  color: #999999;
}

/* 筛选栏 */
.filter-bar.data-v-290fc666 {
  background: #ffffff;
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eeeeee;
}
.filter-item.data-v-290fc666 {
  display: flex;
  align-items: center;
  margin-right: 40rpx;
  padding: 10rpx 20rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
}
.filter-text.data-v-290fc666 {
  font-size: 26rpx;
  color: #333333;
  margin-right: 8rpx;
}
.filter-arrow.data-v-290fc666 {
  font-size: 20rpx;
  color: #999999;
}

/* 房源列表 */
.house-list.data-v-290fc666 {
  flex: 1;
  padding: 20rpx 30rpx;
}
.list-content.data-v-290fc666 {
  padding-bottom: 40rpx;
}

/* 加载状态 */
.load-status.data-v-290fc666 {
  text-align: center;
  padding: 40rpx 0;
}
.status-text.data-v-290fc666 {
  font-size: 26rpx;
  color: #999999;
}

/* 空状态 */
.empty-state.data-v-290fc666 {
  text-align: center;
  padding: 120rpx 0;
}
.empty-text.data-v-290fc666 {
  font-size: 32rpx;
  color: #666666;
  margin-bottom: 20rpx;
  display: block;
}
.empty-desc.data-v-290fc666 {
  font-size: 26rpx;
  color: #999999;
  display: block;
}

