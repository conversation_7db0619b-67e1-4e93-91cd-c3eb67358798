<template>
  <view class="container" v-if="houseDetail">
    <!-- 图片轮播 -->
    <swiper class="image-swiper" indicator-dots circular>
      <swiper-item v-for="(image, index) in houseDetail.images" :key="index">
        <image :src="image" mode="aspectFill" class="house-image" />
      </swiper-item>
    </swiper>

    <!-- 房源基本信息 -->
    <view class="house-info">
      <view class="house-title">{{ houseDetail.title }}</view>

      <view class="price-info">
        <text class="price">¥{{ houseDetail.price }}</text>
        <text class="price-unit">/月</text>
      </view>

      <view class="house-details">
        <view class="detail-item" v-if="houseDetail.area">
          <text class="detail-label">面积：</text>
          <text class="detail-value">{{ houseDetail.area }}㎡</text>
        </view>
        <view class="detail-item" v-if="houseDetail.floor">
          <text class="detail-label">楼层：</text>
          <text class="detail-value">{{ houseDetail.floor }}</text>
        </view>
        <view class="detail-item" v-if="houseDetail.orientation">
          <text class="detail-label">朝向：</text>
          <text class="detail-value">{{ houseDetail.orientation }}</text>
        </view>
      </view>

      <view class="house-tags" v-if="houseDetail.tags && houseDetail.tags.length > 0">
        <text class="tag" v-for="(tag, index) in houseDetail.tags" :key="index">
          {{ tag }}
        </text>
      </view>
    </view>

    <!-- 房源描述 -->
    <view class="section">
      <view class="section-title">房源描述</view>
      <text class="description">{{ houseDetail.description || '暂无描述' }}</text>
    </view>

    <!-- 配套设施 -->
    <view class="section" v-if="houseDetail.facilities && houseDetail.facilities.length > 0">
      <view class="section-title">配套设施</view>
      <view class="facilities">
        <view class="facility-item" v-for="(facility, index) in houseDetail.facilities" :key="index">
          <text class="facility-text">{{ facility }}</text>
        </view>
      </view>
    </view>

    <!-- 位置信息 -->
    <view class="section">
      <view class="section-title">位置信息</view>
      <text class="address">{{ houseDetail.address }}</text>
    </view>

    <!-- 联系信息 -->
    <view class="section" v-if="houseDetail.contact">
      <view class="section-title">联系方式</view>
      <view class="contact-info">
        <text class="contact-name">{{ houseDetail.contact.name || '房东' }}</text>
        <text class="contact-phone">{{ houseDetail.contact.phone }}</text>
      </view>
    </view>

    <!-- 浏览统计 -->
    <view class="stats">
      <text class="stats-text">{{ houseDetail.viewCount || 0 }}人浏览过</text>
      <text class="stats-text">发布于{{ formatDate(houseDetail.createdAt) }}</text>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <view class="action-btn favorite-btn" @click="toggleFavorite">
        <text class="action-icon">{{ houseDetail.isFavorited ? '❤️' : '🤍' }}</text>
        <text class="action-text">{{ houseDetail.isFavorited ? '已收藏' : '收藏' }}</text>
      </view>

      <view class="action-btn contact-btn" @click="contactOwner">
        <text class="action-icon">📞</text>
        <text class="action-text">联系房东</text>
      </view>

      <view class="action-btn appointment-btn" @click="makeAppointment">
        <text class="action-icon">📅</text>
        <text class="action-text">预约看房</text>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" v-else-if="loading">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 错误状态 -->
  <view class="error" v-else>
    <text class="error-text">房源不存在或已下架</text>
    <button class="back-btn" @click="goBack">返回</button>
  </view>
</template>

<script>
export default {
  data() {
    return {
      houseId: '',
      houseDetail: null,
      loading: true
    }
  },

  onLoad(options) {
    if (options.id) {
      this.houseId = options.id
      this.loadHouseDetail()
    } else {
      this.loading = false
    }
  },

  methods: {
    // 加载房源详情
    async loadHouseDetail() {
      this.loading = true

      try {
        const result = await uniCloud.callFunction({
          name: 'getHouseDetail',
          data: {
            houseId: this.houseId
          }
        })

        if (result.result.code === 0) {
          this.houseDetail = result.result.data
        } else {
          uni.showToast({
            title: result.result.message || '加载失败',
            icon: 'none'
          })
        }

      } catch (error) {
        console.error('加载房源详情失败:', error)
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 切换收藏状态
    async toggleFavorite() {
      if (!getApp().checkNeedLogin()) return

      try {
        const result = await uniCloud.callFunction({
          name: 'toggleFavorite',
          data: {
            houseId: this.houseId
          }
        })

        if (result.result.code === 0) {
          this.houseDetail.isFavorited = !this.houseDetail.isFavorited
          uni.showToast({
            title: this.houseDetail.isFavorited ? '收藏成功' : '取消收藏',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: result.result.message || '操作失败',
            icon: 'none'
          })
        }

      } catch (error) {
        console.error('切换收藏状态失败:', error)
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      }
    },

    // 联系房东
    contactOwner() {
      if (!this.houseDetail.contact || !this.houseDetail.contact.phone) {
        uni.showToast({
          title: '暂无联系方式',
          icon: 'none'
        })
        return
      }

      uni.makePhoneCall({
        phoneNumber: this.houseDetail.contact.phone
      })
    },

    // 预约看房
    makeAppointment() {
      if (!getApp().checkNeedLogin()) return

      uni.navigateTo({
        url: `/pages/appointment/appointment?houseId=${this.houseId}`
      })
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    },

    // 返回上一页
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style scoped>
.container {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 图片轮播 */
.image-swiper {
  width: 100%;
  height: 500rpx;
}

.house-image {
  width: 100%;
  height: 100%;
}

/* 房源基本信息 */
.house-info {
  background: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.house-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  line-height: 1.4;
}

.price-info {
  display: flex;
  align-items: baseline;
  margin-bottom: 20rpx;
}

.price {
  font-size: 48rpx;
  font-weight: bold;
  color: #ff6b6b;
}

.price-unit {
  font-size: 28rpx;
  color: #999999;
  margin-left: 8rpx;
}

.house-details {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-right: 40rpx;
  margin-bottom: 10rpx;
}

.detail-label {
  font-size: 26rpx;
  color: #666666;
}

.detail-value {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
}

.house-tags {
  display: flex;
  flex-wrap: wrap;
}

.tag {
  background: #f0f2ff;
  color: #667eea;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
  margin-bottom: 10rpx;
}

/* 通用区块 */
.section {
  background: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.description {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}

/* 配套设施 */
.facilities {
  display: flex;
  flex-wrap: wrap;
}

.facility-item {
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 12rpx 20rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}

.facility-text {
  font-size: 26rpx;
  color: #333333;
}

/* 位置和联系信息 */
.address {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.5;
}

.contact-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.contact-name {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.contact-phone {
  font-size: 28rpx;
  color: #667eea;
  font-weight: 500;
}

/* 浏览统计 */
.stats {
  background: #ffffff;
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.stats-text {
  font-size: 24rpx;
  color: #999999;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  display: flex;
  padding: 20rpx 30rpx;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  margin: 0 10rpx;
  border-radius: 12rpx;
}

.favorite-btn {
  background: #f8f9fa;
}

.contact-btn {
  background: #667eea;
}

.appointment-btn {
  background: #ff6b6b;
}

.action-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.action-text {
  font-size: 24rpx;
  color: #333333;
}

.contact-btn .action-text,
.appointment-btn .action-text {
  color: #ffffff;
}

/* 加载和错误状态 */
.loading, .error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #f5f5f5;
}

.loading-text, .error-text {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 40rpx;
}

.back-btn {
  background: #667eea;
  color: #ffffff;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
}
</style>