<template>
  <view class="register-container">
    <view class="register-header">
      <text class="page-title">注册账号</text>
      <text class="page-desc">加入毕业租房，开始找房之旅</text>
    </view>
    
    <view class="register-form">
      <view class="form-item">
        <input 
          class="form-input" 
          type="text" 
          placeholder="请输入用户名（3-20个字符）"
          v-model="formData.username"
          :disabled="loading"
        />
      </view>
      
      <view class="form-item">
        <input 
          class="form-input" 
          type="password" 
          placeholder="请输入密码（至少6个字符）"
          v-model="formData.password"
          :disabled="loading"
        />
      </view>
      
      <view class="form-item">
        <input 
          class="form-input" 
          type="password" 
          placeholder="请确认密码"
          v-model="formData.confirmPassword"
          :disabled="loading"
        />
      </view>
      
      <view class="form-item">
        <input 
          class="form-input" 
          type="text" 
          placeholder="请输入昵称（可选）"
          v-model="formData.nickname"
          :disabled="loading"
        />
      </view>
      
      <view class="form-item">
        <input 
          class="form-input" 
          type="text" 
          placeholder="请输入手机号（可选）"
          v-model="formData.phone"
          :disabled="loading"
        />
      </view>
      
      <view class="form-item">
        <input 
          class="form-input" 
          type="text" 
          placeholder="请输入邮箱（可选）"
          v-model="formData.email"
          :disabled="loading"
        />
      </view>
      
      <button 
        class="register-btn" 
        :class="{ 'register-btn-disabled': loading }"
        :disabled="loading"
        @click="handleRegister"
      >
        {{ loading ? '注册中...' : '注册' }}
      </button>
      
      <view class="form-footer">
        <text class="login-link" @click="goToLogin">已有账号？立即登录</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        username: '',
        password: '',
        confirmPassword: '',
        nickname: '',
        phone: '',
        email: ''
      },
      loading: false
    }
  },
  
  methods: {
    // 表单验证
    validateForm() {
      const { username, password, confirmPassword, phone, email } = this.formData
      
      if (!username.trim()) {
        uni.showToast({
          title: '请输入用户名',
          icon: 'none'
        })
        return false
      }
      
      if (username.length < 3 || username.length > 20) {
        uni.showToast({
          title: '用户名长度应在3-20个字符之间',
          icon: 'none'
        })
        return false
      }
      
      if (!password.trim()) {
        uni.showToast({
          title: '请输入密码',
          icon: 'none'
        })
        return false
      }
      
      if (password.length < 6) {
        uni.showToast({
          title: '密码长度不能少于6个字符',
          icon: 'none'
        })
        return false
      }
      
      if (password !== confirmPassword) {
        uni.showToast({
          title: '两次输入的密码不一致',
          icon: 'none'
        })
        return false
      }
      
      // 验证手机号格式（如果填写）
      if (phone && !/^1[3-9]\d{9}$/.test(phone)) {
        uni.showToast({
          title: '手机号格式不正确',
          icon: 'none'
        })
        return false
      }
      
      // 验证邮箱格式（如果填写）
      if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        uni.showToast({
          title: '邮箱格式不正确',
          icon: 'none'
        })
        return false
      }
      
      return true
    },
    
    // 处理注册
    async handleRegister() {
      if (!this.validateForm()) {
        return
      }
      
      this.loading = true
      
      try {
        const { confirmPassword, ...registerData } = this.formData
        
        // 清理空字段
        Object.keys(registerData).forEach(key => {
          if (!registerData[key].trim()) {
            delete registerData[key]
          } else {
            registerData[key] = registerData[key].trim()
          }
        })
        
        const result = await uniCloud.callFunction({
          name: 'registerUser',
          data: registerData
        })
        
        if (result.result.code === 0) {
          // 注册成功，保存用户信息和token
          const { token, userInfo } = result.result.data
          
          // 保存到本地存储
          uni.setStorageSync('token', token)
          uni.setStorageSync('userInfo', userInfo)
          
          // 更新全局状态
          getApp().globalData.token = token
          getApp().globalData.userInfo = userInfo
          getApp().globalData.isLogin = true
          
          uni.showToast({
            title: '注册成功',
            icon: 'success'
          })
          
          // 延迟跳转
          setTimeout(() => {
            uni.reLaunch({
              url: '/pages/index/index'
            })
          }, 1500)
          
        } else {
          uni.showToast({
            title: result.result.message || '注册失败',
            icon: 'none'
          })
        }
        
      } catch (error) {
        console.error('注册错误:', error)
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    
    // 跳转到登录页面
    goToLogin() {
      uni.navigateBack()
    }
  }
}
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0 60rpx;
  padding-top: 100rpx;
}

.register-header {
  text-align: center;
  margin-bottom: 80rpx;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 20rpx;
}

.page-desc {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.register-form {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.form-item {
  margin-bottom: 30rpx;
}

.form-input {
  width: 100%;
  height: 88rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 30rpx;
  font-size: 32rpx;
  color: #333333;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #667eea;
  background: #ffffff;
}

.register-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-top: 20rpx;
}

.register-btn-disabled {
  opacity: 0.6;
}

.form-footer {
  text-align: center;
  margin-top: 40rpx;
}

.login-link {
  color: #667eea;
  font-size: 28rpx;
}
</style>
