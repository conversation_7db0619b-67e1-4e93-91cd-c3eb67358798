<view class="login-container data-v-b237504c"><view class="login-header data-v-b237504c"><image class="logo data-v-b237504c" src="/static/logo.png" mode="aspectFit"></image><text class="app-name data-v-b237504c">毕业租房</text><text class="app-desc data-v-b237504c">找到心仪的住所</text></view><view class="login-form data-v-b237504c"><view class="form-item data-v-b237504c"><input class="form-input data-v-b237504c" type="text" placeholder="请输入用户名/手机号" disabled="{{loading}}" data-event-opts="{{[['input',[['__set_model',['$0','username','$event',[]],['formData']]]]]}}" value="{{formData.username}}" bindinput="__e"/></view><view class="form-item data-v-b237504c"><input class="form-input data-v-b237504c" type="password" placeholder="请输入密码" disabled="{{loading}}" data-event-opts="{{[['input',[['__set_model',['$0','password','$event',[]],['formData']]]]]}}" value="{{formData.password}}" bindinput="__e"/></view><button class="{{['login-btn','data-v-b237504c',(loading)?'login-btn-disabled':'']}}" disabled="{{loading}}" data-event-opts="{{[['tap',[['handleLogin',['$event']]]]]}}" bindtap="__e">{{''+(loading?'登录中...':'登录')+''}}</button><view class="form-footer data-v-b237504c"><text data-event-opts="{{[['tap',[['goToRegister',['$event']]]]]}}" class="register-link data-v-b237504c" bindtap="__e">还没有账号？立即注册</text></view></view></view>