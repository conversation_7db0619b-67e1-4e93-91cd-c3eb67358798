<template>
  <view class="login-container">
    <view class="login-header">
      <image class="logo" src="/static/logo.png" mode="aspectFit"></image>
      <text class="app-name">毕业租房</text>
      <text class="app-desc">找到心仪的住所</text>
    </view>
    
    <view class="login-form">
      <view class="form-item">
        <input 
          class="form-input" 
          type="text" 
          placeholder="请输入用户名/手机号"
          v-model="formData.username"
          :disabled="loading"
        />
      </view>
      
      <view class="form-item">
        <input 
          class="form-input" 
          type="password" 
          placeholder="请输入密码"
          v-model="formData.password"
          :disabled="loading"
        />
      </view>
      
      <button 
        class="login-btn" 
        :class="{ 'login-btn-disabled': loading }"
        :disabled="loading"
        @click="handleLogin"
      >
        {{ loading ? '登录中...' : '登录' }}
      </button>
      
      <view class="form-footer">
        <text class="register-link" @click="goToRegister">还没有账号？立即注册</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        username: '',
        password: ''
      },
      loading: false
    }
  },
  
  methods: {
    // 表单验证
    validateForm() {
      if (!this.formData.username.trim()) {
        uni.showToast({
          title: '请输入用户名',
          icon: 'none'
        })
        return false
      }
      
      if (!this.formData.password.trim()) {
        uni.showToast({
          title: '请输入密码',
          icon: 'none'
        })
        return false
      }
      
      if (this.formData.username.length < 3) {
        uni.showToast({
          title: '用户名长度不能少于3个字符',
          icon: 'none'
        })
        return false
      }
      
      if (this.formData.password.length < 6) {
        uni.showToast({
          title: '密码长度不能少于6个字符',
          icon: 'none'
        })
        return false
      }
      
      return true
    },
    
    // 处理登录
    async handleLogin() {
      if (!this.validateForm()) {
        return
      }
      
      this.loading = true
      
      try {
        const result = await uniCloud.callFunction({
          name: 'loginUser',
          data: {
            username: this.formData.username.trim(),
            password: this.formData.password.trim()
          }
        })
        
        if (result.result.code === 0) {
          // 登录成功，保存用户信息和token
          const { token, userInfo } = result.result.data
          
          // 保存到本地存储
          uni.setStorageSync('token', token)
          uni.setStorageSync('userInfo', userInfo)
          
          // 更新全局状态
          getApp().globalData.token = token
          getApp().globalData.userInfo = userInfo
          getApp().globalData.isLogin = true
          
          uni.showToast({
            title: '登录成功',
            icon: 'success'
          })
          
          // 延迟跳转，让用户看到成功提示
          setTimeout(() => {
            // 返回上一页或跳转到首页
            const pages = getCurrentPages()
            if (pages.length > 1) {
              uni.navigateBack()
            } else {
              uni.reLaunch({
                url: '/pages/index/index'
              })
            }
          }, 1500)
          
        } else {
          uni.showToast({
            title: result.result.message || '登录失败',
            icon: 'none'
          })
        }
        
      } catch (error) {
        console.error('登录错误:', error)
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    
    // 跳转到注册页面
    goToRegister() {
      uni.navigateTo({
        url: '/pages/register/register'
      })
    }
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0 60rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.login-header {
  text-align: center;
  margin-bottom: 100rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.app-name {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 20rpx;
}

.app-desc {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.login-form {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.form-item {
  margin-bottom: 40rpx;
}

.form-input {
  width: 100%;
  height: 88rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 30rpx;
  font-size: 32rpx;
  color: #333333;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #667eea;
  background: #ffffff;
}

.login-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-top: 20rpx;
}

.login-btn-disabled {
  opacity: 0.6;
}

.form-footer {
  text-align: center;
  margin-top: 40rpx;
}

.register-link {
  color: #667eea;
  font-size: 28rpx;
}
</style>
